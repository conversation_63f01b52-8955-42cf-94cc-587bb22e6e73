<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.BoxFlowLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.BoxFlowLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="boxNo" property="boxNo" jdbcType="VARCHAR"/>
        <result column="boxUnid" property="boxUnid" jdbcType="VARCHAR"/>
        <result column="nextLineId" property="nextLineId" jdbcType="BIGINT"/>
        <result column="nextStance" property="nextStance" jdbcType="VARCHAR"/>
        <result column="nextStanceGroup" property="nextStanceGroup" jdbcType="VARCHAR"/>
        <result column="isUsed" property="isUsed" jdbcType="INTEGER"/>
        <result column="tzid" property="tzid" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, boxNo, boxUnid, nextLineId, nextStance, nextStanceGroup, isUsed, tzid
    </sql>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.example.entity.BoxFlowLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ems_t_box_flowLog (
            boxNo, boxUnid, nextLineId, nextStance, nextStanceGroup, isUsed, tzid
        ) VALUES (
            #{boxNo,jdbcType=VARCHAR},
            #{boxUnid,jdbcType=VARCHAR},
            #{nextLineId,jdbcType=BIGINT},
            #{nextStance,jdbcType=VARCHAR},
            #{nextStanceGroup,jdbcType=VARCHAR},
            #{isUsed,jdbcType=INTEGER},
            #{tzid,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ems_t_box_flowLog (
            boxNo, boxUnid, nextLineId, nextStance, nextStanceGroup, isUsed, tzid
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.boxNo,jdbcType=VARCHAR},
                #{item.boxUnid,jdbcType=VARCHAR},
                #{item.nextLineId,jdbcType=BIGINT},
                #{item.nextStance,jdbcType=VARCHAR},
                #{item.nextStanceGroup,jdbcType=VARCHAR},
                #{item.isUsed,jdbcType=INTEGER},
                #{item.tzid,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ems_t_box_flowLog
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据箱子编号查询 -->
    <select id="selectByBoxNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ems_t_box_flowLog
        WHERE boxNo = #{boxNo,jdbcType=VARCHAR}
    </select>

    <!-- 根据箱子唯一标识查询 -->
    <select id="selectByBoxUnid" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ems_t_box_flowLog
        WHERE boxUnid = #{boxUnid,jdbcType=VARCHAR}
    </select>

    <!-- 查询所有（包含逻辑删除） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ems_t_box_flowLog
        ORDER BY id DESC
    </select>

    <!-- 查询所有有效记录（排除逻辑删除） -->
    <select id="selectAllValid" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ems_t_box_flowLog
        WHERE nextLineId > 0
        ORDER BY id DESC
    </select>

    <!-- 查询所有逻辑删除的记录 -->
    <select id="selectAllDeleted" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ems_t_box_flowLog
        WHERE nextLineId &lt; 0
        ORDER BY id DESC
    </select>

    <!-- 根据boxUnid查询（排除逻辑删除） -->
    <select id="selectValidByBoxUnid" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ems_t_box_flowLog
        WHERE boxUnid = #{boxUnid,jdbcType=VARCHAR}
          AND nextLineId > 0
    </select>

    <!-- 更新 -->
    <update id="update" parameterType="com.example.entity.BoxFlowLog">
        UPDATE ems_t_box_flowLog
        SET
            boxNo = #{boxNo,jdbcType=VARCHAR},
            boxUnid = #{boxUnid,jdbcType=VARCHAR},
            nextLineId = #{nextLineId,jdbcType=BIGINT},
            nextStance = #{nextStance,jdbcType=VARCHAR},
            nextStanceGroup = #{nextStanceGroup,jdbcType=VARCHAR},
            isUsed = #{isUsed,jdbcType=INTEGER},
            tzid = #{tzid,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据boxUnid更新 -->
    <update id="updateByBoxUnid" parameterType="com.example.entity.BoxFlowLog">
        UPDATE ems_t_box_flowLog
        SET
            boxNo = #{boxNo,jdbcType=VARCHAR},
            nextLineId = #{nextLineId,jdbcType=BIGINT},
            nextStance = #{nextStance,jdbcType=VARCHAR},
            nextStanceGroup = #{nextStanceGroup,jdbcType=VARCHAR},
            isUsed = #{isUsed,jdbcType=INTEGER},
            tzid = #{tzid,jdbcType=VARCHAR}
        WHERE boxUnid = #{boxUnid,jdbcType=VARCHAR}
    </update>

    <!-- 逻辑删除（根据boxUnid） - 将数值字段设置为负数 -->
    <update id="logicalDeleteByBoxUnid" parameterType="java.lang.String">
        UPDATE ems_t_box_flowLog
        SET
            nextLineId = -ABS(nextLineId),
            isUsed = -ABS(isUsed)
        WHERE boxUnid = #{boxUnid,jdbcType=VARCHAR}
          AND nextLineId > 0
    </update>

    <!-- 逻辑删除（根据ID） - 将数值字段设置为负数 -->
    <update id="logicalDeleteById" parameterType="java.lang.Long">
        UPDATE ems_t_box_flowLog
        SET
            nextLineId = -ABS(nextLineId),
            isUsed = -ABS(isUsed)
        WHERE id = #{id,jdbcType=BIGINT}
          AND nextLineId > 0
    </update>

    <!-- 恢复逻辑删除（根据boxUnid） - 将负数字段恢复为正数 -->
    <update id="restoreByBoxUnid" parameterType="java.lang.String">
        UPDATE ems_t_box_flowLog
        SET
            nextLineId = ABS(nextLineId),
            isUsed = ABS(isUsed)
        WHERE boxUnid = #{boxUnid,jdbcType=VARCHAR}
          AND nextLineId &lt; 0
    </update>

    <!-- 恢复逻辑删除（根据ID） - 将负数字段恢复为正数 -->
    <update id="restoreById" parameterType="java.lang.Long">
        UPDATE ems_t_box_flowLog
        SET
            nextLineId = ABS(nextLineId),
            isUsed = ABS(isUsed)
        WHERE id = #{id,jdbcType=BIGINT}
          AND nextLineId &lt; 0
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ems_t_box_flowLog
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

</mapper>
