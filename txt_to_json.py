#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将txt文件内容转换为JSON格式的Python脚本
"""

import json
import sys
import os
from typing import Dict, Any


def read_txt_file(file_path: str) -> str:
    """
    读取txt文件内容
    
    Args:
        file_path: txt文件路径
        
    Returns:
        文件内容字符串
        
    Raises:
        FileNotFoundError: 文件不存在
        UnicodeDecodeError: 文件编码错误
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read().strip()
        return content
    except UnicodeDecodeError:
        # 如果UTF-8解码失败，尝试GBK编码
        try:
            with open(file_path, 'r', encoding='gbk') as file:
                content = file.read().strip()
            return content
        except UnicodeDecodeError:
            raise UnicodeDecodeError(f"无法解码文件 {file_path}，请检查文件编码")


def parse_json_content(content: str) -> Dict[str, Any]:
    """
    解析JSON内容
    
    Args:
        content: 文件内容字符串
        
    Returns:
        解析后的JSON对象
        
    Raises:
        json.JSONDecodeError: JSON格式错误
    """
    try:
        # 直接解析JSON
        json_data = json.loads(content)
        return json_data
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        print("尝试修复常见的JSON格式问题...")
        
        # 尝试修复一些常见问题
        fixed_content = content.strip()
        
        # 如果内容被双重转义，尝试解析data字段
        try:
            temp_data = json.loads(fixed_content)
            if isinstance(temp_data, dict) and 'data' in temp_data:
                if isinstance(temp_data['data'], str):
                    # data字段是字符串，需要再次解析
                    temp_data['data'] = json.loads(temp_data['data'])
            return temp_data
        except:
            pass
        
        raise json.JSONDecodeError(f"无法解析JSON内容: {e}")


def save_json_file(json_data: Dict[str, Any], output_path: str) -> None:
    """
    保存JSON数据到文件
    
    Args:
        json_data: JSON数据
        output_path: 输出文件路径
    """
    with open(output_path, 'w', encoding='utf-8') as file:
        json.dump(json_data, file, ensure_ascii=False, indent=2)


def main():
    """主函数"""
    # 直接读取data.txt文件
    input_file = "data.txt"
    output_file = "data.json"

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 '{input_file}' 不存在")
        print("请确保当前目录下有 data.txt 文件")
        sys.exit(1)
    
    try:
        # 读取txt文件
        print(f"正在读取文件: {input_file}")
        content = read_txt_file(input_file)
        
        if not content:
            print("警告: 文件内容为空")
            sys.exit(1)
        
        # 解析JSON
        print("正在解析JSON内容...")
        json_data = parse_json_content(content)
        
        # 保存JSON文件
        print(f"正在保存到: {output_file}")
        save_json_file(json_data, output_file)
        
        print("转换完成!")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        
        # 显示JSON结构概览
        print("\nJSON结构概览:")
        if isinstance(json_data, dict):
            for key, value in json_data.items():
                if isinstance(value, str) and len(value) > 50:
                    print(f"  {key}: {type(value).__name__} (长度: {len(value)})")
                elif isinstance(value, (list, dict)):
                    print(f"  {key}: {type(value).__name__} (长度: {len(value)})")
                else:
                    print(f"  {key}: {value}")
        
    except FileNotFoundError:
        print(f"错误: 无法找到文件 '{input_file}'")
        sys.exit(1)
    except UnicodeDecodeError as e:
        print(f"错误: {e}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式错误 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"未知错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
