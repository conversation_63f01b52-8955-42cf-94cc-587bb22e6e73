{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6e639b3927bc4f82bc6f38c3dccce4e2", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(<PERSON><PERSON>(button_style='success', description='🟢 Start Recording', style=ButtonStyle()), But<PERSON>(bu…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# AI语音反馈教学助手：语音转文字 + GPT语法纠错 + AI朗读反馈\n", "import openai\n", "from openai import OpenAI\n", "import whisper\n", "import pyttsx3\n", "import sounddevice as sd\n", "import numpy as np\n", "import scipy.io.wavfile as wav\n", "import os\n", "import warnings\n", "import tempfile\n", "import queue\n", "import ipywidgets as widgets\n", "from IPython.display import display, Markdown, HTML\n", "import contextlib\n", "import sys\n", "import logging\n", "import re\n", "from bs4 import BeautifulSoup\n", "\n", "\n", "warnings.filterwarnings('ignore')\n", "logging.getLogger(\"whisper.transcribe\").setLevel(logging.ERROR)\n", "\n", "# === 初始化模块 ===\n", "client = OpenAI(\n", "    api_key=\"********************************************************************************************************************************************************************\",\n", "    base_url=\"https://api.openai.com/v1\",\n", ")\n", "\n", "import torch  \n", "model = whisper.load_model(\"small.en\", device=\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "engine = pyttsx3.init()\n", "engine.setProperty('rate', 150)\n", "engine.setProperty('volume', 1.0)\n", "\n", "\n", "# === 文本转语音 ===\n", "def text_to_speech(text):\n", "    try:\n", "        engine.say(text)\n", "        engine.runAndWait()\n", "    except Exception as e:\n", "        print(\"❌ 播报失败：\", e)\n", "        \n", "def speak_visible_english(html: str):\n", "    EMOJI = re.compile(r\"[\\U0001F300-\\U0001FAFF\\u2600-\\u27BF]+\")\n", "    IPA   = re.compile(r\"/[^/\\n]{1,20}/\")\n", "    CJK   = re.compile(r\"[\\u4e00-\\u9fff]\")\n", "    ONLY_SYMBOLS = re.compile(r\"^[^A-Za-z0-9]+$\")\n", "\n", "    soup = BeautifulSoup(html, \"html.parser\")\n", "    for x in soup.select(\"span.translation, [hidden], [aria-hidden='true'], [style*='display:none'], button, [role='button'], [class*='btn'], [class*='button'], [class*='icon'], .material-icons, [class*='fa-'], [class*='mdi-'], [class*='lucide-'], svg, i, canvas\"):\n", "        x.decompose()\n", "    for t in soup([\"script\",\"style\",\"noscript\"]): t.decompose()\n", "\n", "    txt = soup.get_text(\"\\n\")\n", "    txt = EMOJI.sub(\"\", txt)\n", "    txt = IPA.sub(\"\", txt)\n", "    lines = [re.sub(r\"\\s+\", \" \", ln).strip() for ln in txt.splitlines()]\n", "    lines = [ln for ln in lines if ln and not CJK.search(ln) and not ONLY_SYMBOLS.match(ln) and re.search(r\"[A-Za-z]\", ln)]\n", "    for ln in lines:\n", "        text_to_speech(ln)  # 直接用你已有的 speak()\n", "\n", "    \n", "# === Whisper 识别 ===\n", "def transcribe_audio(filename=\"input.wav\"):\n", "    try:\n", "        with contextlib.redirect_stdout(open(os.devnull, \"w\")), contextlib.redirect_stderr(open(os.devnull, \"w\")):\n", "            result = model.transcribe(filename, language=\"en\", fp16=False, verbose=False)\n", "        return result.get(\"text\", \"\")\n", "    except Exception as e:\n", "        print(\"❌ 识别失败：\", e)\n", "        return \"\"\n", "    finally:\n", "        os.remove(filename)\n", "\n", "# === GPT 反馈 ===\n", "def get_feedback_response(text):\n", "    prompt = \"\"\"你是一位友善且温暖的英语口语教师，请根据以下对话者的英语口语内容判断其语法和语音是否正确。\n", "    \n", "如果语法和语音都正确：\n", "- 用英文表达其语法语音都正确，并给出多样化的表扬语，随机多变的匹配相应的鼓励性的表情包，如：点赞、鼓掌等。\n", "- 根据对话内容，用英文自然地回应。\n", "\n", "如果存在语法和语音错误：\n", "- 请将英文中有误的句子加粗并显示为红色，格式如下：\n", "<strong style='color:red;'>错误句子</strong>\n", "- 根据对话内容，用英文自然回应。\n", "- 最后用英文鼓励其继续努力，鼓励语要丰富，随机加点鼓励性表情包。\n", "\n", "Visible text must be English only. Never output Chinese outside <span class='translation' style='display:none'>…</span>.\n", "Always include both headings: Grammar and Pronunciation. If no reliable issue, say “Your pronunciation is correct.”\n", "Your Sentences: reproduce the learner’s input verbatim (word-for-word). Do not correct or normalize anything there; only add <br> between sentences and the hidden translation spans. No duplication elsewhere.\n", "No placeholders: never output “N/A”, braces, or template tokens.\n", "Grammar:\n", "1) Presence and heading\n", "- The Grammar section must ALWAYS appear under “Language Feedback”.\n", "- Use this exact heading: <strong style=\"color:red;\">Grammar</strong>:\n", "- Visible text must be ENGLISH ONLY. Put any Chinese ONLY inside a hidden span or div:\n", "  <span class='translation' style='display:none;'>(…)</span>\n", "  or <div class=\"translation\" style=\"display:none;\">(…)</div>\n", "2) When errors exist (incorrect branch)\n", "- After the heading, output EXACTLY one black line: {{Grammar_issue_line_en}}\n", "  Set {{Grammar_issue_line_en}} based on error_count:\n", "    • 1  → \"There is a grammar issue.\"\n", "    • ≥2 → \"There are grammar issues.\"\n", "- Output Grammar_issue_line_en exactly. Do not repeat or rephrase this line elsewhere.\n", "- Then enumerate ALL distinct errors (no upper limit) as a numbered list.\n", "  Numbering must start at [1] and increase with NO gaps.\n", "- Each list item MUST follow this format (use straight double quotes):\n", "  [n] \"WRONG\" should be \"<strong style=\"color:red;\">RIGHT</strong>\" — Reason: clear English explanation.\n", "  The WRONG or RIGHT can be a word, phrase, or clause. The Reason may be 1–2 sentences\n", "  in student-friendly English.\n", "- Diff completeness rule: first compose the corrected version, then ensure EVERY change\n", "  between the learner’s text and the corrected version appears once in the list.\n", "  Must cover (not limited to):\n", "  • Pronoun gender and case (he, him, his vs she, her)\n", "  • Subject–auxiliary agreement (am, is, are; was, were)\n", "  • Tense and intention (current intention uses “I want to …”; if “I wanted to …” is not a past desire, correct to “I want to …”)\n", "  • Past-time adverbials → simple past\n", "  • Verb patterns (help + base form; look forward to + gerund; suggest + clause or gerund, etc.)\n", "  • Articles and countability\n", "  • Prepositions\n", "  • Word choice and word order\n", "  • Fragments and run-ons\n", "  • Punctuation and capitalization\n", "- After the numbered list, output EXACTLY one line that includes ONLY the sentences\n", "  that had errors (do NOT include sentences that were already correct):\n", "  It should be: \"<strong style=\"color:red;\">CORRECTED_ERROR_SENTENCES_ONLY</strong>\"\n", "3) Chinese translation block (one block at the end)\n", "- Immediately after the “It should be:” line, output ONE hidden Chinese block that first\n", "  gives the translation of the corrected sentence(s), then numbered translations matching\n", "  [1], [2], [3], … in order, for example:\n", "  <div class=\"translation\" style=\"display:none;\">\n", "  (Corrected translation…)\n", "  ([1] Chinese explanation…)\n", "  ([2] Chinese explanation…)\n", "  ([3] Chinese explanation…)\n", "  </div>\n", "- Do NOT place any other Chinese outside this block.\n", "4) When there are NO errors (correct branch)\n", "- Output ONLY:\n", "  <span style=\"color:#000;font-weight:400;\">Your grammar is correct.</span>\n", "  <div class=\"translation\" style=\"display:none;\">（你的语法是正确的。）</div>\n", "- Do NOT output the numbered list or the “It should be:” line.\n", "5) General constraints\n", "- Preserve the learner’s meaning; do NOT invent new facts (no new names, ages, or events).\n", "- Use straight double quotes \"…\". Do not use curly-brace characters, placeholder text\n", "  (such as “Reason 1”), or “N/A”.\n", "- Place punctuation inside the quoted corrected text when appropriate.\n", "\n", "Pronunciation:\n", "Detect pronunciation issues only from the provided evidence; do not infer beyond data. Default sensitivity: high.\n", "Input\n", "- pron_evidence: array items with word (required), dialect (AmE or BrE; default AmE), obs_ipa (optional), asr_conf (optional 0–1), n_best_tokens (optional array).\n", "- sensitivity: optional, values: high, normal, low.\n", "- Target IPA examples: apple AmE ˈæpəl, BrE ˈæp(ə)l; banana AmE bəˈnænə, BrE bəˈnɑːnə. Parentheses mark optional segments; either form counts as correct.\n", "\n", "Rules (per item)\n", "1) Set target_ipa by dialect; accept optional segments either present or absent.\n", "2) If obs_ipa is present: compare at the phoneme level; primary/secondary stress ˈ/ˌ must match. Report exactly one reason, choosing by this priority: Stress misplaced → Vowel error → Consonant error → Cluster deletion → Final consonant omitted or unreleased. When evidence allows, add:\n", "   - detail_en / detail_zh (e.g., “æ→ɑː in stressed syllable”, “primary stress on 1st not 2nd”)\n", "   - phon_note_en / phon_note_zh (e.g., “iː vs ɪ tense–lax; alveolar stop deletion in cluster”)\n", "   - diff_ipa (compact difference like “æ→ɑː”, “/sts/→/s/”, “ˈ→(none)”)\n", "   - stress_note_en / stress_note_zh (if stress differs)\n", "   - tip_en / tip_zh (one actionable cue)\n", "   - minimal_pairs / minimal_pairs_zh (up to three contrasts such as “ship–sheep”)\n", "3) If obs_ipa is missing: apply sensitivity thresholds using asr_conf and n_best_tokens. High: flag when asr_conf < 0.95 or the target is not consistently top-1 in n_best_tokens (less than 70 percent). Normal: 0.88 and 60 percent. Low: 0.80 and 50 percent. If patterns justify, map to vowel change, consonant change, cluster deletion, or final consonant missing; otherwise use \"Unclear from ASR\" and add a brief asr_note_en / asr_note_zh explaining the uncertainty (e.g., low confidence, unstable top-1).\n", "4) Never fabricate problems; if evidence aligns strongly with the target, do not flag. One reason per item only.\n", "\n", "Output (JSON only; compact; exact keys)\n", "- has_pron_issues: boolean.\n", "- If true:\n", "  - Pron_issue_line_en: exactly \"There is a pronunciation issue.\" for one item, or \"There are pronunciation issues.\" for two or more.\n", "  - Pron_issue_line_zh: exactly \"存在发音问题。\"\n", "  - Pron_items: array in input order; each object includes:\n", "    word;\n", "    target_ipa (IPA without slashes, for example ˈæpəl);\n", "    reason_en: one of \"Vowel error\" | \"Stress misplaced\" | \"Consonant error\" | \"Cluster deletion\" | \"Final consonant omitted/unreleased\" | \"Unclear from ASR\";\n", "    reason_zh: the matching Chinese label.\n", "    Optional fields when available: obs_ipa; diff_ipa; stress_note_en/zh; detail_en/zh; phon_note_en/zh; tip_en/zh; minimal_pairs (array); minimal_pairs_zh (array); asr_note_en/zh.\n", "- If false: return only an object with has_pron_issues set to false.\n", "Formatting: return valid JSON only; no code fences; no extra fields; keys must match exactly.\n", "\n", "Respond to the meaning of the corrected sentence(s) only. Write 3–4 concise English sentences that (1) acknowledge, (2) build/relate, (3) give a tiny tip/bridge, (4) end with an open question to keep the conversation going. Do not repeat or paraphrase the learner’s or the corrected sentences, and do not mention grammar, corrections, or give praise/judgment. Render all English on one line inside a single green bold span (no <br>). On the next line, output one hidden Chinese line that concatenates the translations using\n", "<span class='translation' style='display:none;'>(…)</span>.\n", "Comments: after each English sentence, append one encouragement emoji, chosen　differently. No repeats in the section; vary across runs. English on one line (no extra <br>); then one hidden Chinese line with the concatenated translations.\n", "Translations: Put ALL Chinese ONLY inside <span class='translation' style='display:none;'>（…）</span>. Never output Chinese outside that span; no ASCII parentheses. \n", "\n", "Error checklist (run silently; don’t show this list):\n", "Subject–auxiliary agreement (SVA): I→am; you/we/they→are; he/she/it→is. Questions: “What are you doing?”, “Where is she going?” Flag “you is/was”, “what is you doing”, etc.\n", "Time adverbials & tense: yesterday/last week/… ago/last + unit/in 2010/when I was… → simple past.\n", "since/for (period) + up to now/ever/never/already/just/yet → usually present perfect (accept AmE simple past only with specific past time).\n", "Verb form after common triggers: help + base (“help her find”), look forward to V-ing, suggest (that) + clause / V-ing, enjoy V-ing, stop V-ing / to V (meaning differs).\n", "Fragments/run-ons: ensure each sentence has finite verb; fix comma splices briefly.\n", "Articles/determiners & countability: a/an, the/zero, much/many, few/little.\n", "Common word choice confusions: say/tell; speak/talk; borrow/lend; fun/funny; another/other/others; because/because of.\n", "Prepositions (core): at/on/in (time), in/at/on (place), to/for differences.\n", "Pronoun case & agreement: me/I, he/him; everyone is; data (allow modern plural/singular).\n", "Spelling that flips meaning: fight/find; form/from, etc. Correct only in Grammar.\n", "Capitalization & basic punctuation (first-word caps, end punctuation).\n", "\n", "Few-shot anchors (do not display):\n", "“I go to Singapore last week.” → “I went to Singapore last week.” (past-time marker)\n", "“What is you doing now?” → “What are you doing now?” (SVA)\n", "“I look forward to see you.” → “I look forward to seeing you.” (to + V-ing)\n", "“I helped her fight her umbrella.” → “I helped her find her umbrella.” (word choice)\n", "\n", "请根据以下对话内容，直接生成一段完整的 HTML 卡片。不要输出任何解释或多余文本。不要出现 “N/A”。不要重复回显学习者的句子到多个位置（只在 “Your Sentences” 中展示一次）。\n", "\n", "<div style=\"background:#f9f9f9; border-left:6px solid #2196F3; padding:15px; border-radius:10px; line-height:1.7; font-size:16px; font-family:Arial;\">\n", "    <p> 📝 <strong>Your Sentences</strong><br>{text}</p>\n", "    <!-- 逐句“原样复制” {text}，只在句末加 <br>，绝不改词、绝不改时态、绝不自动纠错。>\n", "    <span class='translation' style='display:none;'>（这里是动态生成的中文翻译）</span>\n", "    </p>\n", "    <!-- Language Feedback：始终包含 Grammar 与 Pronunciation 两段 -->\n", "    <p> 🔍 <strong>Language Feedback</strong><br>\n", "    <!-- Grammar（标题红粗；黑字提示；编号逐条；最后只给“出错句”的正确版本） -->\n", "    <strong style=\"color:red;\">Grammar</strong>:\n", "    Grammar_issue_line_en = ('Your grammar is correct.' if error_count == 0 else\n", "    ('There is a grammar issue.' if error_count == 1 else 'There are grammar issues.'))\n", "    <span style=\"color:#000;font-weight:400;\">{{Grammar_issue_line_en}}</span><br>\n", "    <ul style=\"margin:4px 0 0 18px; padding-left:0; list-style:none;\">\n", "    <li>[1] \"{{Err1_wrong}}\" should be \"<strong style=\"color:red;\">{{Err1_right}}</strong>\" — Reason: {{Err1_reason_en}}.</li>\n", "    <li>[2] \"{{Err2_wrong}}\" should be \"<strong style=\"color:red;\">{{Err2_right}}</strong>\" — Reason: {{Err2_reason_en}}.</li>\n", "    <li>[3] \"{{Err3_wrong}}\" should be \"<strong style=\"color:red;\">{{Err3_right}}</strong>\" — Reason: {{Err3_reason_en}}.</li>\n", "    <!-- 如有更多，继续 [4]、[5]…，确保连续编号 -->\n", "    </ul>\n", "    <p style=\"margin:4px 0 0 0;\">\n", "    It should be: \"<strong style=\"color:red;\">{{Corrected_sentences_only}}</strong>\"\n", "    </p>\n", "    <div class=\"translation\" style=\"display:none;\">\n", "   （存在语法问题。[1] {{Err1_reason_zh}}。[2] {{Err2_reason_zh}}。[3] {{Err3_reason_zh}}。应改为：{{Corrected_sentences_only_zh}}。）\n", "    </div>\n", "    <br>   \n", "    <strong style=\"color:red;\">Pronunciation</strong>:   \n", "    <!-- Pronunciation：标题永远红粗；中文紧跟英文后面；无隐藏翻译块 -->\n", "    <span style=\"color:red !important; font-weight:700 !important;\">Pronunciation</span>:\n", "    {{#has_pron_issues}}<span style=\"color:#000;font-weight:400;\">{{Pron_issue_line_en}} （{{Pron_issue_line_zh}}）</span>{{/has_pron_issues}}\n", "    {{^has_pron_issues}}<span style=\"color:#000;font-weight:400;\">No obvious pronunciation issues. （未发现明显发音问题。）</span>{{/has_pron_issues}}<br>\n", "    {{#has_pron_issues}}\n", "    <ol style=\"margin:4px 0 0 18px; padding-left:0;\">\n", "    {{#Pron_items}}\n", "    <li>\n", "    “{{word}}” should be “<strong style=\"color:red;\">/{{target_ipa}}/</strong>”\n", "    — Reason: {{reason_en}}（{{reason_zh}}）.\n", "    <span style=\"color:#666;\">\n", "      {{#obs_ipa}} Observed: {{obs_ipa}}.{{/obs_ipa}}\n", "      {{#diff_ipa}} Diff: {{diff_ipa}}.{{/diff_ipa}}\n", "      {{#stress_note_en}} Stress: {{stress_note_en}}{{#stress_note_zh}}（{{stress_note_zh}}）{{/stress_note_zh}}.{{/stress_note_en}}\n", "      {{#phon_note_en}} Phonetics: {{phon_note_en}}{{#phon_note_zh}}（{{phon_note_zh}}）{{/phon_note_zh}}.{{/phon_note_en}}\n", "      {{#detail_en}} Detail: {{detail_en}}{{#detail_zh}}（{{detail_zh}}）{{/detail_zh}}.{{/detail_en}}\n", "      {{#tip_en}} Tip: {{tip_en}}{{#tip_zh}}（{{tip_zh}}）{{/tip_zh}}.{{/tip_en}}\n", "      {{#minimal_pairs}} Minimal pairs: {{.}}.{{/minimal_pairs}}\n", "      {{#asr_note_en}} ASR: {{asr_note_en}}{{#asr_note_zh}}（{{asr_note_zh}}）{{/asr_note_zh}}.{{/asr_note_en}}\n", "    </span>\n", "    </li>\n", "    {{/Pron_items}}\n", "    </ol>\n", "    {{/has_pron_issues}}\n", "\n", "    <!-- Let's Chat：绿粗、不换行；每句后仍有隐藏中文 -->\n", "    <p> 📞 <strong>Let's Chat</strong><br>\n", "  　<strong style=\"color:green;\">{{R1}} {{R2}} {{R3}} {{R4_OPT}}</strong><br>\n", " 　 <span class='translation' style='display:none;'>（{{R1_zh}} {{R2_zh}} {{R3_zh}} {{R4_OPT_zh}}）</span>\n", "    </p>\n", "    <!-- Comments：单段落；每句结尾附1个鼓励类表情（多变、不重复）；每句后跟隐藏中文 -->\n", "    <p> 🚀 <strong>Comments</strong><br>\n", "  　{{C1}} 🎉 {{C2}} 💪 {{C3_OPT}} 🌟 {{C4_OPT}} 😊<br>\n", "  　<span class='translation' style='display:none;'>（{{C1_zh}} {{C2_zh}} {{C3_OPT_zh}} {{C4_OPT_zh}}）</span>\n", "</div>\n", "\n", "翻译标签应统一使用 class='translation' 并设置 display:none；点击下方按钮后统一显示。\n", "\n", "按钮格式如下，放置在卡片底部：\n", "<button id='toggleBtn' onclick='toggleTranslation()' style='margin-top:10px; margin-right:10px; float:right; padding:6px 12px; border-radius:6px; background:#3f88c5; color:white; border:none; cursor:pointer;'>🔁 Show Translation</button>\n", "<script>\n", "  var shown = false;\n", "  function toggleTranslation() {{\n", "    var items = document.getElementsByClassName('translation');\n", "    for (var i = 0; i < items.length; i++) {{\n", "      items[i].style.display = shown ? 'none' : 'inline';\n", "    }}\n", "    shown = !shown;\n", "    document.getElementById('toggleBtn').innerText = shown ? '🔁 Hide Translation' : '🔁 Show Translation';\n", "  }}\n", "</script>\n", "\n", "在点击“🔁 Show Translation”按钮后每个英文句子后面都必须紧跟对应的中文翻译，并使用如下格式嵌入：<span class='translation' style='display:none;'>（中文翻译）</span>。统一显示，按钮请放置在卡片右下角。\n", "务必确保所有英文句子都带有这样的翻译标签。\n", "请勿遗漏任何翻译内容，确保按钮能控制所有 class 为 translation 的内容显隐。\n", "请不要输出其他内容，只输出完整 HTML 卡片。\n", "\"\"\".format(text = text)\n", "    \n", "    \n", "    response = client.chat.completions.create(\n", "        model=\"gpt-4o-mini\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"你是一位友善的英语教师，会用英文判断语法错误并用英文回应学生问题。\"},\n", "            {\"role\": \"user\", \"content\": prompt}\n", "        ],\n", "        max_tokens=1200,\n", "        temperature=0.2\n", "    )\n", "\n", "    gpt_html = response.choices[0].message.content\n", "    avatar_img = \"<img src='files/neza-avatar.png' style='height:100%; max-height:240px; border-radius:20px;'>\"  # ✅ 等比例放大哪吒，使其与卡片高度接近并保持居中\"<img src='files/neza-avatar.png' height='100%' style='margin-right:16px; border-radius:20px; transform: scale(2); transform-origin: center;'>\"  # ✅ 放大哪吒两倍，保持卡片对齐\"<img src='files/neza-avatar.png' height='100%' style='margin-right:16px; border-radius:20px;'>\"  # ✅ 高度撑满卡片左侧\"<img src='files/neza-avatar.png' width='60' style='margin-right:12px; border-radius:50%; vertical-align:top;'>\"\n", "    html = \"\"\"\n", "    <div style='display:flex; align-items:stretch; background:#f0f8ff; border: 4px solid sienna; border-radius:10px; padding:15px;'>\n", "      <div style='width: 110px; display: flex; align-items:center; justify-content:center; padding:8px;'>\n", "        {avatar_img}\n", "      </div>\n", "      <div style='flex:1; padding: 0 12px;'>\n", "        {gpt_html}\n", "      </div>\n", "    </div>\n", "    \"\"\".format(avatar_img=avatar_img, gpt_html=gpt_html)\n", "    return html\n", "\n", "# === 控制录音类 ===\n", "class ControlledRecorder:\n", "    def __init__(self, samplerate=16000):     \n", "        self.samplerate = samplerate\n", "        self.q = queue.Queue()\n", "        self.recording = []\n", "\n", "    def start(self):\n", "        self.recording = []\n", "        self.q = queue.Queue()\n", "        self.recording_output = widgets.Output()\n", "        with self.recording_output:\n", "            display(Markdown(\"**🎧 Recording, speak now...**\"))\n", "        display(self.recording_output)  # ✅ 将提示包装进 Output 容器，便于 stop() 时清除  # ✅ 使用 Markdown 渲染提示，便于后续清除  # ✅ 保留提示，但不再清空所有输出\n", "        self.stream = sd.InputStream(samplerate=self.samplerate, channels=1, dtype='float32', callback=self.callback)\n", "        self.stream.start()\n", "    \n", "    def callback(self, indata, frames, time, status):\n", "        self.q.put(indata.copy())\n", "\n", "    def stop(self):\n", "        try:\n", "            self.stream.stop()\n", "        except:\n", "            return None\n", "        while not self.q.empty():\n", "            self.recording.append(self.q.get())\n", "        if not self.recording:\n", "            return None\n", "        try:\n", "            audio = np.concatenate(self.recording, axis=0)\n", "            audio = audio * 1.5  # 增益1.5倍\n", "            audio = np.clip(audio, -1.0, 1.0)  # 防止削波\n", "            \n", "            # 转换为int16格式保存\n", "            audio_int16 = (audio * 32767).astype(np.int16)\n", "        except:\n", "            return None\n", "        f = tempfile.NamedTemporaryFile(delete=False, suffix=\".wav\")\n", "        wav.write(f.name, self.samplerate, audio_int16)\n", "        return f.name\n", "\n", "# === 控制按钮函数 ===\n", "def on_start_clicked(b):\n", "    recorder.start()  # ✅ 立即开始录音\n", "    countdown_output = widgets.Output()\n", "    display(countdown_output)\n", "    import time\n", "    for i in range(20, 0, -1):  # ✅ 倒计时 20 秒，与录音时长一致\n", "        with countdown_output:\n", "            countdown_output.clear_output(wait=True)\n", "            display(Markdown(f\"<span style='font-size:24px;'>⏳ 正在录音（剩余 {i} 秒）...</span>\"))  # ✅ 放大倒计时提示字体\n", "        time.sleep(1)\n", "    countdown_output.clear_output()\n", "    audio_file = recorder.stop()  # ✅ 20 秒后自动结束录音\n", "    if not audio_file:\n", "        return\n", "    text = transcribe_audio(audio_file)\n", "    if not text.strip():\n", "        return\n", "    if \"close the conversation\" in text.lower():\n", "        print(\"🛑 手动结束对话。再见！\")\n", "        text_to_speech(\"好的，再见！\")\n", "        return\n", "    reply = get_feedback_response(text)\n", "    display(HTML(reply))  # ✅ 渲染 HTML 输出\n", "    speak_visible_english(reply)\n", "    \n", "\n", "def on_stop_clicked(b):\n", "    print(\"🛑 手动结束对话。再见！\")\n", "    text_to_speech(\"好的，再见！\")\n", "\n", "# === UI ===\n", "recorder = ControlledRecorder(samplerate=16000)\n", "start_button = widgets.Button(description=\"🟢 Start Recording\", button_style=\"success\")\n", "stop_button = widgets.<PERSON><PERSON>(description=\"🛑 Stop Recording\", button_style=\"danger\")\n", "\n", "start_button.on_click(on_start_clicked)\n", "stop_button.on_click(on_stop_clicked)\n", "\n", "display(widgets.HBox([start_button, stop_button]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}