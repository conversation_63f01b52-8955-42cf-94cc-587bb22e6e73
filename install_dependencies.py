#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频路由工具依赖安装脚本
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package_name, description=""):
    """安装Python包"""
    print(f"\n正在安装 {package_name}...")
    if description:
        print(f"  用途: {description}")
    
    success, stdout, stderr = run_command(f"pip install {package_name}")
    
    if success:
        print(f"✓ {package_name} 安装成功")
        return True
    else:
        print(f"✗ {package_name} 安装失败")
        if stderr:
            print(f"  错误: {stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    print("=" * 60)
    print("音频路由工具 - 依赖安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 6):
        print("⚠️  警告: 建议使用Python 3.6或更高版本")
    
    # 核心依赖列表
    core_dependencies = [
        ("pyaudio", "音频输入输出处理"),
        ("pydub", "音频文件处理"),
        ("numpy", "数值计算和音频数据处理")
    ]
    
    # 可选依赖列表
    optional_dependencies = [
        ("soundcard", "系统音频捕获功能"),
        ("pycaw", "Windows音频控制（仅Windows）")
    ]
    
    print(f"\n开始安装核心依赖...")
    
    # 安装核心依赖
    core_success = True
    for package, description in core_dependencies:
        if check_package(package.split('>=')[0]):
            print(f"✓ {package} 已安装")
        else:
            if not install_package(package, description):
                core_success = False
    
    if not core_success:
        print(f"\n❌ 核心依赖安装失败，程序可能无法正常运行")
        print("请手动安装失败的依赖包")
        return False
    
    print(f"\n✅ 所有核心依赖安装完成！")
    
    # 询问是否安装可选依赖
    print(f"\n可选依赖安装:")
    
    for package, description in optional_dependencies:
        package_name = package.split('>=')[0]
        if check_package(package_name):
            print(f"✓ {package} 已安装")
            continue
        
        response = input(f"\n是否安装 {package}？({description}) [y/N]: ").strip().lower()
        if response in ['y', 'yes']:
            install_package(package, description)
        else:
            print(f"跳过 {package}")
    
    print(f"\n" + "=" * 60)
    print("安装完成！")
    print("=" * 60)
    
    print(f"\n使用方法:")
    print(f"  python audio_router_cli.py              # 交互模式")
    print(f"  python audio_router_cli.py --list-devices  # 列出设备")
    print(f"  python audio_router_cli.py --auto-start    # 自动启动")
    
    print(f"\n注意事项:")
    print(f"• 如果pyaudio安装失败，可能需要安装Visual Studio Build Tools")
    print(f"• 在某些系统上可能需要管理员权限")
    print(f"• 建议安装VB-Cable虚拟音频设备以获得最佳体验")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n安装被用户中断")
    except Exception as e:
        print(f"\n\n安装脚本出错: {str(e)}")
    
    input(f"\n按回车键退出...")
