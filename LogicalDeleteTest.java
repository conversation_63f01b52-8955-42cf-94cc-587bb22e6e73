package com.example.test;

import com.example.entity.BoxFlowLog;
import com.example.mapper.BoxFlowLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 逻辑删除功能测试类
 */
@Component
public class LogicalDeleteTest {

    @Autowired
    private BoxFlowLogMapper boxFlowLogMapper;

    /**
     * 测试逻辑删除功能
     */
    public void testLogicalDelete() {
        System.out.println("=== 测试逻辑删除功能 ===");

        // 1. 先插入一条测试数据
        BoxFlowLog testLog = BoxFlowLog.builder()
                .boxNo("TEST001")
                .boxUnid("TEST-UNID-001")
                .nextLineId(100L)
                .nextStance("测试站点")
                .nextStanceGroup("测试组")
                .isUsed(1)
                .tzid("12345")
                .build();

        try {
            // 插入测试数据
            boxFlowLogMapper.insert(testLog);
            System.out.println("插入测试数据成功，ID: " + testLog.getId());
            System.out.println("插入的数据: " + testLog);

            // 2. 查询插入的数据
            BoxFlowLog inserted = boxFlowLogMapper.selectById(testLog.getId());
            System.out.println("\n查询到的数据: " + inserted);
            System.out.println("nextLineId: " + inserted.getNextLineId() + " (正数表示有效)");
            System.out.println("isUsed: " + inserted.getIsUsed());

            // 3. 执行逻辑删除
            int deleteResult = boxFlowLogMapper.logicalDeleteById(testLog.getId());
            System.out.println("\n逻辑删除结果: " + deleteResult + " 条记录被标记为删除");

            // 4. 查询逻辑删除后的数据
            BoxFlowLog afterDelete = boxFlowLogMapper.selectById(testLog.getId());
            System.out.println("逻辑删除后的数据: " + afterDelete);
            System.out.println("nextLineId: " + afterDelete.getNextLineId() + " (负数表示已删除)");
            System.out.println("isUsed: " + afterDelete.getIsUsed() + " (负数表示已删除)");

            // 5. 测试有效记录查询（应该查不到）
            List<BoxFlowLog> validRecords = boxFlowLogMapper.selectAllValid();
            boolean foundInValid = validRecords.stream()
                    .anyMatch(log -> log.getId().equals(testLog.getId()));
            System.out.println("\n在有效记录中是否能找到: " + foundInValid + " (应该是false)");

            // 6. 测试删除记录查询（应该能查到）
            List<BoxFlowLog> deletedRecords = boxFlowLogMapper.selectAllDeleted();
            boolean foundInDeleted = deletedRecords.stream()
                    .anyMatch(log -> log.getId().equals(testLog.getId()));
            System.out.println("在删除记录中是否能找到: " + foundInDeleted + " (应该是true)");

            // 7. 恢复逻辑删除
            int restoreResult = boxFlowLogMapper.restoreById(testLog.getId());
            System.out.println("\n恢复删除结果: " + restoreResult + " 条记录被恢复");

            // 8. 查询恢复后的数据
            BoxFlowLog afterRestore = boxFlowLogMapper.selectById(testLog.getId());
            System.out.println("恢复后的数据: " + afterRestore);
            System.out.println("nextLineId: " + afterRestore.getNextLineId() + " (正数表示有效)");
            System.out.println("isUsed: " + afterRestore.getIsUsed());

            // 9. 清理测试数据
            boxFlowLogMapper.deleteById(testLog.getId());
            System.out.println("\n测试数据已清理");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试根据boxUnid的逻辑删除
     */
    public void testLogicalDeleteByBoxUnid() {
        System.out.println("\n=== 测试根据boxUnid的逻辑删除 ===");

        // 插入测试数据
        BoxFlowLog testLog = BoxFlowLog.builder()
                .boxNo("TEST002")
                .boxUnid("TEST-UNID-002")
                .nextLineId(200L)
                .nextStance("测试站点2")
                .nextStanceGroup("测试组2")
                .isUsed(0)
                .tzid("54321")
                .build();

        try {
            // 插入
            boxFlowLogMapper.insert(testLog);
            System.out.println("插入测试数据: " + testLog);

            // 根据boxUnid逻辑删除
            int deleteResult = boxFlowLogMapper.logicalDeleteByBoxUnid("TEST-UNID-002");
            System.out.println("逻辑删除结果: " + deleteResult + " 条记录");

            // 查询有效记录（应该查不到）
            BoxFlowLog validRecord = boxFlowLogMapper.selectValidByBoxUnid("TEST-UNID-002");
            System.out.println("查询有效记录: " + validRecord + " (应该是null)");

            // 查询所有记录（应该能查到，但字段为负数）
            BoxFlowLog allRecord = boxFlowLogMapper.selectByBoxUnid("TEST-UNID-002");
            System.out.println("查询所有记录: " + allRecord);
            if (allRecord != null) {
                System.out.println("nextLineId: " + allRecord.getNextLineId() + " (负数表示已删除)");
            }

            // 恢复
            int restoreResult = boxFlowLogMapper.restoreByBoxUnid("TEST-UNID-002");
            System.out.println("恢复结果: " + restoreResult + " 条记录");

            // 清理
            boxFlowLogMapper.deleteById(testLog.getId());
            System.out.println("测试数据已清理");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 显示逻辑删除统计信息
     */
    public void showStatistics() {
        System.out.println("\n=== 逻辑删除统计信息 ===");

        try {
            List<BoxFlowLog> allRecords = boxFlowLogMapper.selectAll();
            List<BoxFlowLog> validRecords = boxFlowLogMapper.selectAllValid();
            List<BoxFlowLog> deletedRecords = boxFlowLogMapper.selectAllDeleted();

            System.out.println("总记录数: " + allRecords.size());
            System.out.println("有效记录数: " + validRecords.size());
            System.out.println("逻辑删除记录数: " + deletedRecords.size());

            if (!deletedRecords.isEmpty()) {
                System.out.println("\n逻辑删除的记录:");
                for (BoxFlowLog log : deletedRecords) {
                    System.out.println("  ID: " + log.getId() + 
                                     ", boxUnid: " + log.getBoxUnid() + 
                                     ", nextLineId: " + log.getNextLineId());
                }
            }

        } catch (Exception e) {
            System.err.println("获取统计信息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 运行所有测试
     */
    public void runAllTests() {
        System.out.println("开始测试逻辑删除功能...\n");

        testLogicalDelete();
        testLogicalDeleteByBoxUnid();
        showStatistics();

        System.out.println("\n所有逻辑删除测试完成！");
    }
}
