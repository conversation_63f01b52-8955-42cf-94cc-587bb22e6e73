package com.example.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.ToString;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 箱子流转日志实体类
 * 对应数据库表：ems_t_box_flowLog
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
public class BoxFlowLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 箱子编号
     */
    private String boxNo;

    /**
     * 箱子唯一标识
     */
    private String boxUnid;

    /**
     * 下一条线路ID
     */
    private Long nextLineId;

    /**
     * 下一站点
     */
    private String nextStance;

    /**
     * 下一站点组
     */
    private String nextStanceGroup;

    /**
     * 是否已使用 (0-未使用, 1-已使用)
     */
    private Integer isUsed;

    /**
     * 时区ID
     */
    private String tzid;

    /**
     * 检查是否已使用
     * @return true-已使用, false-未使用
     */
    public boolean isUsedFlag() {
        return this.isUsed != null && this.isUsed == 1;
    }

    /**
     * 设置使用状态
     * @param used true-已使用, false-未使用
     */
    public void setUsedFlag(boolean used) {
        this.isUsed = used ? 1 : 0;
    }
}
