/**
 * @this {ThisContext}
 */
export function onMounted() {}

/**
 * 监听回调函数
 * @this {ThisContext}
 * @param {Object} newValue 新值
 * @param {Object} oldValue 旧值
 */
export function watchHandler(newValue, oldValue) {
  console.log('newValue', newValue, 'oldValue', oldValue);

  if (!newValue?.value || newValue.value === '') {
    return;
  }
  this.state.scanConfig.value = newValue.value;
  //判断newValue.value是否是以A开头的字符串 要都转成大写
  if (newValue.value.toUpperCase().startsWith('A')) {
    // 如果扫码的结果是以A开头的字符串则是箱号
    this.state.params.boxNo = newValue.value;
  } else {
    //如果不是以A开头的字符串则是衣架号;
    //如果衣架卡1有值则填充到衣架卡2否则填充到衣架卡1 
    if (this.state.params.hangerId && this.state.params.hangerId.trim() !== '') {
      this.state.params.hangerIdSec = newValue.value;
    } else {
      this.state.params.hangerId = newValue.value;
    }
  }
}

export function onClearn() {
  this.state.scanConfig.value = '';
  this.state.params.boxNo = '';
  this.state.params.hangerId = '';
  this.state.params.hangerIdSec = '';
}

export async function onSave(){
   if(!confirm("确定要保存吗？")){
    return;
  }
  //校验参数
  if (!this.state.params.boxNo) {
    this.$message.error('箱号不能为空');
    return;
  }
  if (this.state.params.hangerId.trim() == "") {
    this.$message.error('衣架卡1不能为空');
    return;
  }
  if (this.state.params.hangerIdSec.trim() == "") {
    this.$message.error('衣架卡2不能为空');
    return;
  }
  if(this.state.params.hangerId == this.state.params.hangerIdSec){
    this.$message.error('衣架卡1和衣架卡2不能相同');
    return;
  }
  this.state.params.tzid = this.state.tzid;
  //接下来执行保存
  //1.开启Loading
  this.$util.showLoading({id: "root__RFcCu0"})
  //2.发送请求
  const res =  await this.$util.fetchDataSource({name: "name"}).then(res => {console.log(res, "res")})
  //3.解析返回值
  if(res.errcode == 0){
    this.$util.showMessage({content: "保存成功！！！", type: "success"})
  }else{
    this.$util.showMessage({content: "res.errmsg", type: "error"})
  }
  //4.关闭loading
  this.
}