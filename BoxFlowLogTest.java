package com.example.test;

import com.example.entity.BoxFlowLog;
import com.example.mapper.BoxFlowLogMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * BoxFlowLog测试类
 */
@Component
public class BoxFlowLogTest {

    @Autowired
    private BoxFlowLogMapper boxFlowLogMapper;

    /**
     * 测试单条插入
     */
    public void testInsertSingle() {
        System.out.println("=== 测试单条插入 ===");
        
        BoxFlowLog log = BoxFlowLog.builder()
                .boxNo("")
                .boxUnid("")
                .nextLineId(40L)
                .nextStance("4")
                .nextStanceGroup("4")
                .isUsed(1)
                .tzid("11360")
                .build();

        try {
            int result = boxFlowLogMapper.insert(log);
            System.out.println("插入结果: " + result);
            System.out.println("生成的ID: " + log.getId());
            System.out.println("插入成功！");
        } catch (Exception e) {
            System.err.println("插入失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试批量插入（从JSON文件读取）
     */
    public void testInsertBatch() {
        System.out.println("=== 测试批量插入 ===");
        
        try {
            // 读取JSON测试数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<BoxFlowLog> testData = objectMapper.readValue(
                new File("box_flow_log_test_data.json"), 
                new TypeReference<List<BoxFlowLog>>() {}
            );

            System.out.println("从JSON文件读取到 " + testData.size() + " 条测试数据");

            // 批量插入
            int result = boxFlowLogMapper.insertBatch(testData);
            System.out.println("批量插入结果: " + result + " 条记录");
            System.out.println("批量插入成功！");

        } catch (Exception e) {
            System.err.println("批量插入失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试逐条插入（从JSON文件读取）
     */
    public void testInsertOneByOne() {
        System.out.println("=== 测试逐条插入 ===");
        
        try {
            // 读取JSON测试数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<BoxFlowLog> testData = objectMapper.readValue(
                new File("box_flow_log_test_data.json"), 
                new TypeReference<List<BoxFlowLog>>() {}
            );

            System.out.println("从JSON文件读取到 " + testData.size() + " 条测试数据");

            // 逐条插入
            int successCount = 0;
            for (int i = 0; i < testData.size(); i++) {
                BoxFlowLog log = testData.get(i);
                try {
                    int result = boxFlowLogMapper.insert(log);
                    if (result > 0) {
                        successCount++;
                        System.out.println("第 " + (i + 1) + " 条插入成功，ID: " + log.getId());
                    }
                } catch (Exception e) {
                    System.err.println("第 " + (i + 1) + " 条插入失败: " + e.getMessage());
                }
            }

            System.out.println("逐条插入完成，成功: " + successCount + "/" + testData.size());

        } catch (Exception e) {
            System.err.println("逐条插入失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试查询功能
     */
    public void testSelect() {
        System.out.println("=== 测试查询功能 ===");
        
        try {
            // 查询所有记录
            List<BoxFlowLog> allLogs = boxFlowLogMapper.selectAll();
            System.out.println("查询到 " + allLogs.size() + " 条记录");

            // 显示前5条记录
            for (int i = 0; i < Math.min(5, allLogs.size()); i++) {
                BoxFlowLog log = allLogs.get(i);
                System.out.println("记录 " + (i + 1) + ": " + log);
            }

        } catch (Exception e) {
            System.err.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 运行所有测试
     */
    public void runAllTests() {
        System.out.println("开始运行BoxFlowLog测试...\n");
        
        // 测试单条插入
        testInsertSingle();
        System.out.println();
        
        // 测试批量插入
        testInsertBatch();
        System.out.println();
        
        // 测试逐条插入
        testInsertOneByOne();
        System.out.println();
        
        // 测试查询
        testSelect();
        
        System.out.println("\n所有测试完成！");
    }
}
