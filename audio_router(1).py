import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pyaudio
import pygame
import threading
import wave
import time
import numpy as np
from pydub import AudioSegment
from pydub.playback import play
import os
import sys
import io

# 尝试导入Windows专用的音频捕获库
try:
    import pycaw
    from pycaw.pycaw import AudioUtilities, AudioSession
    PYCAW_AVAILABLE = True
except ImportError:
    PYCAW_AVAILABLE = False

# 尝试导入soundcard库用于系统音频捕获
try:
    import soundcard as sc
    SOUNDCARD_AVAILABLE = True
except ImportError:
    SOUNDCARD_AVAILABLE = False

class AudioRouter:
    def __init__(self, root):
        self.root = root
        self.root.title("音频路由工具 - VB-Cable伴侣 (增强版)")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 音频相关变量
        self.audio = pyaudio.PyAudio()
        self.routing_stream_in = None
        self.routing_stream_out = None
        self.routing_thread = None
        self.is_routing = False
        self.routing_volume = 1.0
        self.routing_muted = False
        
        # 系统音频捕获相关
        self.system_audio_mode = False
        self.system_mic = None
        
        # MP3播放相关变量
        # 暂时不初始化pygame，等选择输出设备后再初始化
        self.pygame_initialized = False
        self.current_mp3 = None
        self.mp3_paused = False
        self.mp3_volume = 1.0
        self.mp3_position = 0
        
        # 音频设备列表
        self.input_devices = []
        self.output_devices = []
        self.system_audio_devices = []  # 新增：系统音频设备列表
        
        # MP3播放流控制
        self.mp3_stream = None
        self.mp3_thread = None
        self.mp3_playing = False
        self.mp3_audio_data = None
        
        # 初始化界面
        self.setup_ui()
        self.setup_audio_isolation_mode()
        self.refresh_devices()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 设备管理区域
        device_frame = ttk.LabelFrame(main_frame, text="音频设备管理", padding="10")
        device_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N), pady=(0, 10))
        device_frame.columnconfigure(1, weight=1)
        
        # 刷新设备按钮
        ttk.Button(device_frame, text="刷新设备", command=self.refresh_devices).grid(row=0, column=0, sticky=tk.W)
        
        # 系统音频捕获模式开关
        self.system_audio_var = tk.BooleanVar()
        self.system_audio_check = ttk.Checkbutton(
            device_frame, 
            text="系统音频捕获模式 (直接捕获系统声音)", 
            variable=self.system_audio_var,
            command=self.toggle_system_audio_mode
        )
        self.system_audio_check.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        # 输入设备选择
        ttk.Label(device_frame, text="输入设备:").grid(row=1, column=0, sticky=tk.W, pady=(10, 5))
        self.input_device_var = tk.StringVar()
        self.input_device_combo = ttk.Combobox(device_frame, textvariable=self.input_device_var, state="readonly")
        self.input_device_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(10, 5))
        
        # 输出设备选择
        ttk.Label(device_frame, text="输出设备:").grid(row=2, column=0, sticky=tk.W, pady=(5, 5))
        self.output_device_var = tk.StringVar()
        self.output_device_combo = ttk.Combobox(device_frame, textvariable=self.output_device_var, state="readonly")
        self.output_device_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(5, 5))
        
        # 音频路由区域
        routing_frame = ttk.LabelFrame(main_frame, text="音频路由控制", padding="10")
        routing_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N), pady=(0, 10))
        routing_frame.columnconfigure(1, weight=1)
        
        # 路由控制按钮
        button_frame = ttk.Frame(routing_frame)
        button_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        self.start_routing_btn = ttk.Button(button_frame, text="开始路由", command=self.start_routing)
        self.start_routing_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_routing_btn = ttk.Button(button_frame, text="停止路由", command=self.stop_routing, state="disabled")
        self.stop_routing_btn.grid(row=0, column=1, padx=(5, 5))
        
        self.mute_routing_btn = ttk.Button(button_frame, text="静音", command=self.toggle_routing_mute)
        self.mute_routing_btn.grid(row=0, column=2, padx=(5, 0))
        
        # 路由音量控制
        ttk.Label(routing_frame, text="路由音量:").grid(row=1, column=0, sticky=tk.W, pady=(10, 5))
        self.routing_volume_var = tk.DoubleVar(value=100.0)
        self.routing_volume_scale = ttk.Scale(routing_frame, from_=0.0, to=100.0, 
                                            variable=self.routing_volume_var,
                                            command=self.update_routing_volume)
        self.routing_volume_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(10, 5))
        
        # 音频信号监控
        ttk.Label(routing_frame, text="信号强度:").grid(row=2, column=0, sticky=tk.W, pady=(5, 5))
        self.signal_progress = ttk.Progressbar(routing_frame, mode='determinate', maximum=100.0)
        self.signal_progress['value'] = 0.0
        self.signal_progress.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(5, 5))
        
        # MP3播放区域
        mp3_frame = ttk.LabelFrame(main_frame, text="MP3播放器", padding="10")
        mp3_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N), pady=(0, 10))
        mp3_frame.columnconfigure(1, weight=1)
        
        # 文件选择
        file_frame = ttk.Frame(mp3_frame)
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Button(file_frame, text="选择MP3文件", command=self.select_mp3_file).grid(row=0, column=0)
        self.mp3_file_label = ttk.Label(file_frame, text="未选择文件")
        self.mp3_file_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 播放控制按钮
        control_frame = ttk.Frame(mp3_frame)
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        self.play_btn = ttk.Button(control_frame, text="播放", command=self.play_mp3, state="disabled")
        self.play_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.pause_btn = ttk.Button(control_frame, text="暂停", command=self.pause_mp3, state="disabled")
        self.pause_btn.grid(row=0, column=1, padx=(5, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止", command=self.stop_mp3, state="disabled")
        self.stop_btn.grid(row=0, column=2, padx=(5, 5))
        
        self.mute_mp3_btn = ttk.Button(control_frame, text="静音", command=self.toggle_mp3_mute)
        self.mute_mp3_btn.grid(row=0, column=3, padx=(5, 0))
        
        # MP3音量控制
        ttk.Label(mp3_frame, text="播放音量:").grid(row=2, column=0, sticky=tk.W, pady=(10, 5))
        self.mp3_volume_var = tk.DoubleVar(value=100.0)
        self.mp3_volume_scale = ttk.Scale(mp3_frame, from_=0.0, to=100.0,
                                        variable=self.mp3_volume_var,
                                        command=self.update_mp3_volume)
        self.mp3_volume_scale.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(10, 5))
        
        # MP3输出设备选择
        ttk.Label(mp3_frame, text="MP3输出设备:").grid(row=3, column=0, sticky=tk.W, pady=(5, 5))
        self.mp3_output_var = tk.StringVar()
        self.mp3_output_combo = ttk.Combobox(mp3_frame, textvariable=self.mp3_output_var, state="readonly")
        self.mp3_output_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(5, 5))
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
    def refresh_devices(self):
        """刷新音频设备列表"""
        try:
            self.input_devices = []
            self.output_devices = []
            self.system_audio_devices = [] # 清空系统音频设备列表
            
            # 获取设备信息
            device_count = self.audio.get_device_count()
            for i in range(device_count):
                try:
                    device_info = self.audio.get_device_info_by_index(i)
                    device_name = device_info['name']
                    
                    # 跳过一些可能有问题的设备类型
                    if any(skip_word in device_name.lower() for skip_word in 
                           ['microsoft sound mapper', 'primary sound', 'communications']):
                        continue
                    
                    # 创建更清晰的设备显示名称
                    display_name = f"{i}: {device_name}"
                    if device_info.get('hostApi', 0) != -1:
                        try:
                            host_api_info = self.audio.get_host_api_info_by_index(device_info['hostApi'])
                            api_name = host_api_info['name']
                            if api_name not in device_name:
                                display_name += f" ({api_name})"
                        except:
                            pass
                    
                    # 添加输入设备（检查是否真的可用）
                    if device_info['maxInputChannels'] > 0:
                        try:
                            # 简单测试设备是否可用
                            test_stream = self.audio.open(
                                format=pyaudio.paInt16,
                                channels=1,
                                rate=int(device_info['defaultSampleRate']) or 44100,
                                input=True,
                                input_device_index=i,
                                frames_per_buffer=1024
                            )
                            test_stream.close()
                            self.input_devices.append((i, display_name))
                        except:
                            # 如果设备不可用，标记但仍添加
                            self.input_devices.append((i, display_name + " [可能不可用]"))
                    
                    # 添加输出设备（检查是否真的可用）
                    if device_info['maxOutputChannels'] > 0:
                        try:
                            # 简单测试设备是否可用
                            test_stream = self.audio.open(
                                format=pyaudio.paInt16,
                                channels=1,
                                rate=int(device_info['defaultSampleRate']) or 44100,
                                output=True,
                                output_device_index=i,
                                frames_per_buffer=1024
                            )
                            test_stream.close()
                            self.output_devices.append((i, display_name))
                        except:
                            # 如果设备不可用，标记但仍添加
                            self.output_devices.append((i, display_name + " [可能不可用]"))
                            
                except Exception as e:
                    # 跳过有问题的设备
                    print(f"跳过设备 {i}: {str(e)}")
                    continue
            
            # 更新下拉框
            input_names = [name for _, name in self.input_devices]
            output_names = [name for _, name in self.output_devices]
            
            self.input_device_combo['values'] = input_names
            self.output_device_combo['values'] = output_names
            self.mp3_output_combo['values'] = output_names
            
            # 默认选择VB-Cable设备（如果存在）
            vb_cable_found = False
            for i, name in enumerate(input_names):
                if any(vb_word in name.upper() for vb_word in ["CABLE OUTPUT", "VB-CABLE OUTPUT", "CABLE OUT"]):
                    self.input_device_combo.current(i)
                    vb_cable_found = True
                    break
            
            for i, name in enumerate(output_names):
                if any(vb_word in name.upper() for vb_word in ["CABLE INPUT", "VB-CABLE INPUT", "CABLE IN"]):
                    self.output_device_combo.current(i)
                    self.mp3_output_combo.current(i)
                    vb_cable_found = True
                    break
            
            # 如果没有找到VB-Cable，选择默认设备
            if not vb_cable_found:
                if input_names:
                    self.input_device_combo.current(0)
                if output_names:
                    self.output_device_combo.current(0)
                    self.mp3_output_combo.current(0)
            
            status_msg = f"已检测到 {len(self.input_devices)} 个输入设备，{len(self.output_devices)} 个输出设备"
            if vb_cable_found:
                status_msg += " (已自动选择VB-Cable设备)"
            self.status_label.config(text=status_msg)
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新设备失败: {str(e)}\n\n请检查音频驱动是否正常安装")
    
    def get_selected_input_device(self):
        """获取选中的输入设备ID"""
        selection = self.input_device_combo.current()
        if selection >= 0:
            return self.input_devices[selection][0]
        return None
    
    def get_selected_output_device(self):
        """获取选中的输出设备ID"""
        selection = self.output_device_combo.current()
        if selection >= 0:
            return self.output_devices[selection][0]
        return None
    
    def get_selected_mp3_output_device(self):
        """获取选中的MP3输出设备ID"""
        selection = self.mp3_output_combo.current()
        if selection >= 0:
            return self.output_devices[selection][0]
        return None
    
    def start_routing(self):
        """开始音频路由"""
        # 如果是系统音频模式，启动系统音频捕获
        if self.system_audio_mode:
            self.start_system_audio_capture()
            return
        
        # 如果启用了音频隔离模式，使用隔离路由
        if hasattr(self, 'isolation_var') and self.isolation_var.get():
            self.start_isolated_audio_routing()
            return
            
        input_device = self.get_selected_input_device()
        output_device = self.get_selected_output_device()
        
        if input_device is None or output_device is None:
            messagebox.showwarning("警告", "请先选择输入和输出设备")
            return
        
        # 检查设备是否相同，避免反馈
        if input_device == output_device:
            messagebox.showwarning("警告", "输入和输出设备不能相同，这会造成音频反馈")
            return
        
        try:
            # 获取设备信息并确定最佳参数
            input_info = self.audio.get_device_info_by_index(input_device)
            output_info = self.audio.get_device_info_by_index(output_device)
            
            # 动态确定音频参数
            CHUNK = 1024
            FORMAT = pyaudio.paInt16
            
            # 确定通道数 - 使用设备支持的最小通道数
            input_channels = int(input_info['maxInputChannels'])
            output_channels = int(output_info['maxOutputChannels'])
            CHANNELS = min(input_channels, output_channels, 2)  # 最多使用2个通道
            
            if CHANNELS == 0:
                messagebox.showerror("错误", "选择的设备不支持所需的音频通道")
                return
            
            # 确定采样率 - 使用设备默认采样率
            input_rate = int(input_info['defaultSampleRate'])
            output_rate = int(output_info['defaultSampleRate'])
            
            # 选择兼容的采样率
            common_rates = [44100, 48000, 22050, 16000, 8000]
            RATE = input_rate  # 默认使用输入设备采样率
            
            # 如果输入和输出设备采样率不同，选择一个通用的采样率
            if input_rate != output_rate:
                for rate in common_rates:
                    if rate <= max(input_rate, output_rate):
                        RATE = rate
                        break
            
            if RATE < 8000:
                RATE = 44100  # 如果检测到的采样率过低，使用标准采样率
                
            print(f"传统路由使用参数: 采样率={RATE}Hz, 通道={CHANNELS}, 格式=Int16")
            
            self.status_label.config(text=f"尝试连接设备... 采样率:{RATE}Hz 通道:{CHANNELS}")
            
            # 打开输入流 - 添加更多错误处理
            try:
                self.routing_stream_in = self.audio.open(
                    format=FORMAT,
                    channels=CHANNELS,
                    rate=RATE,
                    input=True,
                    input_device_index=input_device,
                    frames_per_buffer=CHUNK
                )
            except Exception as e:
                # 如果立体声失败，尝试单声道
                if CHANNELS == 2:
                    CHANNELS = 1
                    self.routing_stream_in = self.audio.open(
                        format=FORMAT,
                        channels=CHANNELS,
                        rate=RATE,
                        input=True,
                        input_device_index=input_device,
                        frames_per_buffer=CHUNK
                    )
                else:
                    raise e
            
            # 打开输出流
            try:
                self.routing_stream_out = self.audio.open(
                    format=FORMAT,
                    channels=CHANNELS,
                    rate=RATE,
                    output=True,
                    output_device_index=output_device,
                    frames_per_buffer=CHUNK
                )
            except Exception as e:
                # 关闭已打开的输入流
                if self.routing_stream_in:
                    self.routing_stream_in.close()
                    self.routing_stream_in = None
                raise e
            
            # 开始路由线程
            self.is_routing = True
            self.routing_thread = threading.Thread(target=self.routing_worker)
            self.routing_thread.daemon = True
            self.routing_thread.start()
            
            # 更新UI
            self.start_routing_btn.config(state="disabled")
            self.stop_routing_btn.config(state="normal")
            self.status_label.config(text="音频路由运行中...")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动音频路由失败: {str(e)}")
            self.stop_routing()
    
    def stop_routing(self):
        """停止音频路由"""
        # 如果是系统音频模式，停止系统音频捕获
        if self.system_audio_mode:
            self.stop_system_audio_capture()
            return
            
        self.is_routing = False
        
        if self.routing_thread:
            self.routing_thread.join(timeout=1.0)
        
        if self.routing_stream_in:
            self.routing_stream_in.stop_stream()
            self.routing_stream_in.close()
            self.routing_stream_in = None
        
        if self.routing_stream_out:
            self.routing_stream_out.stop_stream()
            self.routing_stream_out.close()
            self.routing_stream_out = None
        
        # 更新UI
        self.start_routing_btn.config(state="normal")
        self.stop_routing_btn.config(state="disabled")
        self.signal_progress['value'] = 0
        self.status_label.config(text="音频路由已停止")
    
    def routing_worker(self):
        """音频路由工作线程"""
        try:
            while self.is_routing:
                # 读取音频数据
                data = self.routing_stream_in.read(1024, exception_on_overflow=False)
                
                if not self.routing_muted:
                    # 应用音量调节
                    if self.routing_volume != 1.0 and len(data) > 0:
                        try:
                            # 转换为numpy数组进行音量调节
                            audio_data = np.frombuffer(data, dtype=np.int16)
                            if len(audio_data) > 0:
                                # 使用float64进行计算避免溢出
                                scaled_data = audio_data.astype(np.float64) * self.routing_volume
                                # 限制在int16范围内
                                scaled_data = np.clip(scaled_data, -32768, 32767)
                                audio_data = scaled_data.astype(np.int16)
                                data = audio_data.tobytes()
                        except (ValueError, MemoryError):
                            # 如果处理失败，使用原始数据
                            pass
                    
                    # 输出音频数据
                    self.routing_stream_out.write(data)
                    
                    # 计算信号强度
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    if len(audio_data) > 0:
                        # 计算RMS信号强度
                        rms = np.sqrt(np.mean(audio_data.astype(np.float64)**2))
                        signal_level = min(100, max(0, (rms / 32768.0) * 100))
                        
                        # 检查是否为有效数值
                        if np.isfinite(signal_level) and not np.isnan(signal_level):
                            self.root.after(0, lambda level=signal_level: self.signal_progress.config(value=level))
                        else:
                            self.root.after(0, lambda: self.signal_progress.config(value=0))
                    else:
                        self.root.after(0, lambda: self.signal_progress.config(value=0))
                else:
                    # 静音时输出静音数据
                    silence = b'\x00' * len(data)
                    self.routing_stream_out.write(silence)
                    self.root.after(0, lambda: self.signal_progress.config(value=0))
                
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"音频路由出错: {str(e)}"))
            self.root.after(0, self.stop_routing)
    
    def update_routing_volume(self, value):
        """更新路由音量"""
        try:
            volume = float(value) / 100.0
            # 确保音量值在有效范围内
            if np.isfinite(volume) and not np.isnan(volume):
                self.routing_volume = max(0.0, min(1.0, volume))
            else:
                self.routing_volume = 0.0
        except (ValueError, TypeError):
            self.routing_volume = 0.0
    
    def toggle_routing_mute(self):
        """切换路由静音状态"""
        self.routing_muted = not self.routing_muted
        self.mute_routing_btn.config(text="取消静音" if self.routing_muted else "静音")
    
    def select_mp3_file(self):
        """选择MP3文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP3文件",
            filetypes=[("MP3文件", "*.mp3"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.current_mp3 = file_path
            filename = os.path.basename(file_path)
            self.mp3_file_label.config(text=filename)
            
            # 启用播放控制按钮
            self.play_btn.config(state="normal")
            self.pause_btn.config(state="normal")
            self.stop_btn.config(state="normal")
    
    def play_mp3(self):
        """播放MP3文件到选定的输出设备"""
        if not self.current_mp3:
            messagebox.showwarning("警告", "请先选择MP3文件")
            return
        
        try:
            if self.mp3_playing and self.mp3_paused:
                # 恢复播放
                self.mp3_paused = False
                self.play_btn.config(text="暂停")
                self.status_label.config(text="MP3播放已恢复")
            elif self.mp3_playing:
                # 暂停播放
                self.mp3_paused = True
                self.play_btn.config(text="播放")
                self.status_label.config(text="MP3播放已暂停")
            else:
                # 开始播放 - 使用选定的输出设备
                self.start_mp3_playback()
                
        except Exception as e:
            messagebox.showerror("错误", f"播放MP3失败: {str(e)}")
    
    def start_mp3_playback(self):
        """使用选定的输出设备开始MP3播放"""
        try:
            output_device = self.get_selected_output_device()
            if output_device is None:
                messagebox.showwarning("警告", "请先选择输出设备")
                return
            
            # 获取输出设备信息
            device_info = self.audio.get_device_info_by_index(output_device)
            device_name = device_info.get('name', '未知设备')
            
            # 加载MP3文件
            audio_segment = AudioSegment.from_mp3(self.current_mp3)
            
            # 转换为PyAudio兼容的格式
            self.mp3_audio_data = audio_segment.raw_data
            self.mp3_sample_rate = audio_segment.frame_rate
            self.mp3_channels = audio_segment.channels
            self.mp3_sample_width = audio_segment.sample_width
            
            # 计算PyAudio格式
            if self.mp3_sample_width == 1:
                audio_format = pyaudio.paUInt8
            elif self.mp3_sample_width == 2:
                audio_format = pyaudio.paInt16
            elif self.mp3_sample_width == 4:
                audio_format = pyaudio.paInt32
            else:
                audio_format = pyaudio.paInt16  # 默认格式
            
            # 打开音频流到指定的输出设备
            self.mp3_stream = self.audio.open(
                format=audio_format,
                channels=self.mp3_channels,
                rate=self.mp3_sample_rate,
                output=True,
                output_device_index=output_device,
                frames_per_buffer=1024
            )
            
            # 开始播放线程
            self.mp3_playing = True
            self.mp3_paused = False
            self.mp3_thread = threading.Thread(target=self.mp3_playback_worker)
            self.mp3_thread.daemon = True
            self.mp3_thread.start()
            
            self.play_btn.config(text="暂停")
            self.status_label.config(text=f"MP3播放到: {device_name}")
            messagebox.showinfo("成功", f"MP3将播放到选定的输出设备:\n{device_name}")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动MP3播放失败: {str(e)}")
    
    def mp3_playback_worker(self):
        """MP3播放工作线程"""
        try:
            chunk_size = 1024 * self.mp3_channels * self.mp3_sample_width
            data_len = len(self.mp3_audio_data)
            position = 0
            
            while self.mp3_playing and position < data_len:
                if not self.mp3_paused:
                    # 读取音频数据块
                    end_pos = min(position + chunk_size, data_len)
                    chunk = self.mp3_audio_data[position:end_pos]
                    
                    if len(chunk) > 0:
                        # 应用音量调节
                        if self.mp3_volume != 1.0:
                            # 转换为numpy数组进行音量调节
                            if self.mp3_sample_width == 2:
                                audio_array = np.frombuffer(chunk, dtype=np.int16)
                                audio_array = (audio_array * self.mp3_volume).astype(np.int16)
                                chunk = audio_array.tobytes()
                        
                        # 播放音频块
                        self.mp3_stream.write(chunk)
                        position = end_pos
                    else:
                        break
                else:
                    # 暂停状态，等待
                    time.sleep(0.1)
            
            # 播放完成
            self.mp3_playing = False
            self.root.after(0, self.mp3_playback_finished)
            
        except Exception as e:
            self.mp3_playing = False
            print(f"MP3播放线程错误: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("错误", f"MP3播放错误: {str(e)}"))
    
    def mp3_playback_finished(self):
        """MP3播放完成后的清理"""
        self.play_btn.config(text="播放")
        self.status_label.config(text="MP3播放完成")
        if self.mp3_stream:
            self.mp3_stream.stop_stream()
            self.mp3_stream.close()
            self.mp3_stream = None
    
    def pause_mp3(self):
        """暂停MP3播放"""
        try:
            if self.mp3_playing and not self.mp3_paused:
                self.mp3_paused = True
                self.play_btn.config(text="继续")
                self.status_label.config(text="MP3播放已暂停")
            
        except Exception as e:
            messagebox.showerror("错误", f"暂停MP3失败: {str(e)}")
    
    def stop_mp3(self):
        """停止MP3播放"""
        try:
            self.mp3_playing = False
            self.mp3_paused = False
            
            if self.mp3_thread:
                self.mp3_thread.join(timeout=1.0)
            
            if self.mp3_stream:
                self.mp3_stream.stop_stream()
                self.mp3_stream.close()
                self.mp3_stream = None
            
            self.play_btn.config(text="播放")
            self.status_label.config(text="MP3播放已停止")
            
        except Exception as e:
            messagebox.showerror("错误", f"停止MP3失败: {str(e)}")
    
    def update_mp3_volume(self, value):
        """更新MP3音量"""
        try:
            volume = float(value) / 100.0
            # 确保音量值在有效范围内
            if np.isfinite(volume) and not np.isnan(volume):
                self.mp3_volume = max(0.0, min(1.0, volume))
            else:
                self.mp3_volume = 0.0
            
            # 新的音量控制系统不需要pygame的set_volume
            # 音量在播放线程中实时应用
        except (ValueError, TypeError):
            self.mp3_volume = 0.0
    
    def toggle_mp3_mute(self):
        """切换MP3静音状态"""
        try:
            if self.mp3_volume > 0:
                self.mp3_prev_volume = self.mp3_volume
                self.mp3_volume = 0.0
                self.mp3_volume_scale.set(0)
                self.mute_mp3_btn.config(text="取消静音")
            else:
                prev_volume = getattr(self, 'mp3_prev_volume', 0.7)
                self.mp3_volume = prev_volume
                self.mp3_volume_scale.set(prev_volume * 100)
                self.mute_mp3_btn.config(text="静音")
                
        except Exception as e:
            messagebox.showerror("错误", f"MP3静音控制失败: {str(e)}")
    
    def toggle_system_audio_mode(self):
        """切换系统音频捕获模式"""
        self.system_audio_mode = self.system_audio_var.get()
        
        if self.system_audio_mode:
            # 启用系统音频捕获模式
            if not SOUNDCARD_AVAILABLE:
                messagebox.showerror("错误", "系统音频捕获功能需要soundcard库\n请运行: pip install soundcard")
                self.system_audio_var.set(False)
                self.system_audio_mode = False
                return
                
            self.status_label.config(text="系统音频捕获模式已启用 - 点击开始路由开始捕获")
            # 在系统音频模式下，输入设备选择被禁用
            self.input_device_combo.config(state="disabled")
        else:
            # 禁用系统音频捕获模式
            if self.is_routing:
                self.stop_routing()
            self.status_label.config(text="已切换到普通音频路由模式")
            self.input_device_combo.config(state="readonly")
    
    def start_system_audio_capture(self):
        """启动系统音频捕获"""
        if not SOUNDCARD_AVAILABLE:
            messagebox.showerror("错误", "系统音频捕获需要soundcard库。请运行: pip install soundcard")
            return
        
        try:
            output_device = self.get_selected_output_device()
            if output_device is None:
                messagebox.showwarning("警告", "请先选择输出设备")
                return
            
            # 获取输出设备信息以确定最佳音频参数
            output_info = self.audio.get_device_info_by_index(output_device)
            print(f"输出设备信息: {output_info}")
            
            # 确定最佳音频参数
            output_channels = int(output_info['maxOutputChannels'])
            output_rate = int(output_info['defaultSampleRate'])
            
            # 选择合适的通道数（1或2）
            channels = min(2, output_channels) if output_channels > 0 else 2
            
            # 选择合适的采样率
            if output_rate < 8000:
                sample_rate = 44100  # 使用标准采样率
            elif output_rate > 48000:
                sample_rate = 48000  # 限制最高采样率
            else:
                sample_rate = output_rate
            
            print(f"使用音频参数: 采样率={sample_rate}Hz, 通道={channels}, 格式=Int16")
            
            # 获取系统默认音频设备（loopback模式）
            default_speaker = sc.default_speaker()
            
            # 创建系统音频录制器（loopback）- 使用与输出匹配的参数
            self.system_mic = sc.get_microphone(id=str(default_speaker.id), include_loopback=True)
            
            # 保存音频参数供worker使用
            self.capture_sample_rate = sample_rate
            self.capture_channels = channels
            self.capture_output_device = output_device
            
            # 开始系统音频捕获线程
            self.is_routing = True
            self.routing_thread = threading.Thread(target=self.system_audio_worker)
            self.routing_thread.daemon = True
            self.routing_thread.start()
            
            # 更新UI状态
            self.start_routing_btn.config(state="disabled") 
            self.stop_routing_btn.config(state="normal")
            self.status_label.config(text=f"系统音频捕获运行中... ({sample_rate}Hz, {channels}ch)")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动系统音频捕获失败: {str(e)}\n请确保安装了soundcard库: pip install soundcard")
            self.system_audio_mode = False
            self.system_audio_var.set(False)
    
    def stop_system_audio_capture(self):
        """停止系统音频捕获"""
        self.is_routing = False
        
        if self.routing_thread:
            self.routing_thread.join(timeout=1.0)
        
        if self.system_mic:
            self.system_mic = None
        
        # 更新UI状态
        self.start_routing_btn.config(state="normal")
        self.stop_routing_btn.config(state="disabled") 
        self.signal_progress['value'] = 0
        self.status_label.config(text="系统音频捕获已停止")
    
    def system_audio_worker(self):
        """系统音频捕获工作线程"""
        if not SOUNDCARD_AVAILABLE:
            return
            
        try:
            # 使用保存的音频参数
            sample_rate = getattr(self, 'capture_sample_rate', 44100)
            channels = getattr(self, 'capture_channels', 2)
            output_device = getattr(self, 'capture_output_device', None)
            
            if output_device is None:
                return
                
            print(f"系统音频工作线程启动: {sample_rate}Hz, {channels}ch")
            
            # 打开输出流 - 使用Int16格式确保兼容性
            output_stream = self.audio.open(
                format=pyaudio.paInt16,  # 改为Int16格式，兼容性更好
                channels=channels,
                rate=sample_rate,
                output=True,
                output_device_index=output_device,
                frames_per_buffer=1024
            )
            
            # 开始录制系统音频 - 使用匹配的参数
            with self.system_mic.recorder(samplerate=sample_rate, channels=channels) as recorder:
                while self.is_routing:
                    try:
                        # 录制音频数据
                        data = recorder.record(numframes=1024)
                        
                        if not self.routing_muted and len(data) > 0:
                            # 应用音量调节
                            if self.routing_volume != 1.0:
                                data = data * self.routing_volume
                            
                            # 确保数据格式正确
                            if len(data.shape) == 1:
                                # 单声道数据，如果需要立体声则复制
                                if channels == 2:
                                    data = np.column_stack((data, data))
                            elif len(data.shape) == 2 and data.shape[1] > channels:
                                # 如果录制的通道数多于输出通道数，进行混音
                                data = np.mean(data[:, :channels], axis=1, keepdims=True)
                                if channels == 2:
                                    data = np.column_stack((data.flatten(), data.flatten()))
                            
                            # 转换为Int16格式并限制范围
                            audio_data = np.clip(data * 32767, -32768, 32767).astype(np.int16)
                            audio_bytes = audio_data.tobytes()
                            
                            # 输出音频数据
                            output_stream.write(audio_bytes)
                            
                            # 计算并更新信号强度
                            rms = np.sqrt(np.mean(data**2))
                            signal_strength = min(100, rms * 1000)  # 调整缩放因子
                            
                            # 更新UI（在主线程中）
                            self.root.after(0, lambda s=signal_strength: self.signal_progress.config(value=s))
                    
                    except Exception as e:
                        print(f"系统音频捕获错误: {str(e)}")
                        continue
            
            # 关闭输出流
            output_stream.stop_stream()
            output_stream.close()
            print("系统音频捕获线程正常结束")
            
        except Exception as e:
            error_msg = f"系统音频捕获工作线程错误: {str(e)}"
            print(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

    def setup_audio_isolation_mode(self):
        """设置音频隔离模式，避免系统音频混音"""
        isolation_frame = ttk.LabelFrame(self.root, text="音频隔离设置", padding="10")
        isolation_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # 音频隔离选项
        self.isolation_var = tk.BooleanVar()
        isolation_check = ttk.Checkbutton(
            isolation_frame,
            text="启用音频隔离模式 (防止系统音频混入)",
            variable=self.isolation_var,
            command=self.toggle_audio_isolation
        )
        isolation_check.grid(row=0, column=0, sticky=tk.W)
        
        # 提示标签
        isolation_help = ttk.Label(
            isolation_frame,
            text="启用此选项可防止其他应用程序的音频混入录音中",
            foreground="gray"
        )
        isolation_help.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        # 简化提示 - 现在MP3会直接播放到选定的输出设备
        mp3_info_help = ttk.Label(
            isolation_frame,
            text="现在MP3音频会直接播放到程序中选定的输出设备",
            foreground="green"
        )
        mp3_info_help.grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
    
    def toggle_audio_isolation(self):
        """切换音频隔离模式"""
        if self.isolation_var.get():
            # 启用音频隔离模式
            if self.system_audio_mode:
                # 如果当前是系统音频模式，提醒用户
                messagebox.showinfo("提示", 
                    "音频隔离模式已启用。\n"
                    "建议关闭'系统音频捕获模式'以避免混音问题。\n"
                    "使用普通音频路由模式可以更好地隔离音频源。")
            
            # 重置警告标志
            self._mp3_isolation_warning_shown = False
            
            # 显示使用说明
            messagebox.showinfo("音频隔离模式说明", 
                "音频隔离模式已启用！\n\n"
                "功能说明：\n"
                "• 麦克风音频 → 路由到选定输出设备\n"
                "• MP3音频 → 播放到系统默认设备\n"
                "• 后端应用音频 → 不会混入录音\n\n"
                "如需MP3音频也录入虚拟设备：\n"
                "• 方案1：临时设置系统默认设备为VB-Cable Input\n"
                "• 方案2：关闭隔离模式，使用系统音频捕获模式\n"
                "• 方案3：在录音前暂停MP3播放")
        else:
            # 禁用音频隔离模式
            messagebox.showinfo("提示", "音频隔离模式已禁用")
    

    
    def start_isolated_audio_routing(self):
        """启动隔离的音频路由（只路由特定输入设备）"""
        try:
            input_device = self.get_selected_input_device()
            output_device = self.get_selected_output_device()
            
            if input_device is None or output_device is None:
                messagebox.showwarning("警告", "请选择输入和输出设备")
                return
            
            # 确保输入和输出设备不同，避免反馈
            if input_device == output_device:
                messagebox.showwarning("警告", "输入和输出设备不能相同")
                return
            
            # 标准音频参数
            FORMAT = pyaudio.paInt16
            CHANNELS = 2
            RATE = 44100
            CHUNK = 1024
            
            # 打开输入流（只从选定的输入设备）
            self.routing_stream_in = self.audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                input_device_index=input_device,
                frames_per_buffer=CHUNK
            )
            
            # 打开输出流
            self.routing_stream_out = self.audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                output=True,
                output_device_index=output_device,
                frames_per_buffer=CHUNK
            )
            
            # 开始隔离路由线程
            self.is_routing = True
            self.routing_thread = threading.Thread(target=self.isolated_routing_worker)
            self.routing_thread.daemon = True
            self.routing_thread.start()
            
            # 更新UI
            self.start_routing_btn.config(state="disabled")
            self.stop_routing_btn.config(state="normal")
            self.status_label.config(text="音频隔离路由运行中...")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动隔离音频路由失败: {str(e)}")
            self.stop_routing()
    
    def isolated_routing_worker(self):
        """隔离音频路由工作线程 - 只处理麦克风输入"""
        try:
            while self.is_routing:
                # 读取来自麦克风的音频数据
                mic_data = self.routing_stream_in.read(1024, exception_on_overflow=False)
                
                # 处理麦克风音频
                if not self.routing_muted and len(mic_data) > 0:
                    try:
                        # 应用音量调节
                        if self.routing_volume != 1.0:
                            mic_audio = np.frombuffer(mic_data, dtype=np.int16)
                            if len(mic_audio) > 0:
                                scaled_mic = mic_audio.astype(np.float64) * self.routing_volume
                                scaled_mic = np.clip(scaled_mic, -32768, 32767)
                                mic_data = scaled_mic.astype(np.int16).tobytes()
                    except (ValueError, MemoryError):
                        pass
                    
                    # 输出音频数据
                    self.routing_stream_out.write(mic_data)
                    
                    # 计算信号强度
                    mic_audio = np.frombuffer(mic_data, dtype=np.int16)
                    if len(mic_audio) > 0:
                        rms = np.sqrt(np.mean(mic_audio.astype(np.float64)**2))
                        signal_level = min(100, max(0, (rms / 32768.0) * 100))
                        
                        if np.isfinite(signal_level) and not np.isnan(signal_level):
                            self.root.after(0, lambda sl=signal_level: self.signal_progress.config(value=sl))
                
        except Exception as e:
            error_msg = f"隔离音频路由错误: {str(e)}"
            print(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
    
    def __del__(self):
        """清理资源"""
        self.stop_routing()
        self.stop_mp3()  # 停止MP3播放
        if hasattr(self, 'audio'):
            self.audio.terminate()

def main():
    root = tk.Tk()
    app = AudioRouter(root)
    
    def on_closing():
        app.stop_routing()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main() 