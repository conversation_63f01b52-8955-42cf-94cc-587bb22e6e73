# 音频路由工具 - 命令行版

VB-Cable伴侣工具的纯命令行版本，保持所有GUI版本的核心功能。

## 功能特性

### 🎵 核心功能
- **音频路由**: 实时将输入设备音频路由到输出设备
- **MP3播放器**: 内置MP3播放功能，可选择特定输出设备
- **系统音频捕获**: 直接捕获系统播放的音频（需要soundcard库）
- **音频隔离模式**: 防止系统音频混入录音

### 🎛️ 控制功能
- 音量控制（路由和MP3独立控制）
- 静音功能
- 实时状态显示
- 设备自动检测和选择

## 安装依赖

### 必需依赖
```bash
pip install pyaudio pydub numpy
```

### 可选依赖
```bash
# 系统音频捕获功能
pip install soundcard

# Windows音频控制（可选）
pip install pycaw
```

## 使用方法

### 基本使用
```bash
# 交互模式（推荐）
python audio_router_cli.py

# 列出所有音频设备
python audio_router_cli.py --list-devices

# 自动启动路由（使用默认设备）
python audio_router_cli.py --auto-start
```

### 主要操作流程

1. **设备配置**
   - 选择输入设备（麦克风或VB-Cable Output）
   - 选择输出设备（扬声器或VB-Cable Input）
   - 选择MP3输出设备

2. **音频路由**
   - 开始/停止音频路由
   - 调整路由音量
   - 静音控制

3. **MP3播放**
   - 选择MP3文件
   - 播放/暂停/停止控制
   - 独立音量控制

## 使用场景

### 🎙️ 直播/录制场景
```
配置建议:
- 输入设备: 物理麦克风
- 输出设备: VB-Cable Input
- MP3输出: 物理扬声器

效果: 麦克风音频进入录音软件，背景音乐只有你能听到
```

### 🎵 音频混音场景
```
配置建议:
- 输入设备: VB-Cable Output
- 输出设备: VB-Cable Input
- 启用系统音频捕获模式

效果: 捕获所有系统音频并路由到虚拟设备
```

### 🔇 音频隔离场景
```
配置建议:
- 启用音频隔离模式
- 分别设置麦克风和MP3的输出设备

效果: 完全分离不同音频源，避免混音
```

## 菜单说明

### 主菜单选项
- `1` - 显示音频设备列表
- `2` - 选择输入设备
- `3` - 选择输出设备
- `4` - 选择MP3输出设备
- `5` - 显示当前状态
- `6` - 音频路由控制
- `7` - MP3播放控制
- `8` - 系统音频捕获设置
- `9` - 音频隔离模式设置
- `0` - 退出程序

### 快捷键
- `Ctrl+C` - 安全退出程序（自动清理资源）

## 技术特性

### 🔧 音频处理
- 自动检测设备最佳音频参数
- 智能采样率和通道数适配
- 实时音量调节和信号处理
- 多线程音频处理，避免阻塞

### 🛡️ 错误处理
- 完善的异常处理机制
- 设备兼容性检查
- 资源自动清理

### 🎨 用户界面
- 彩色命令行界面
- 清晰的状态显示
- 用户友好的交互设计

## 故障排除

### 常见问题

**Q: 提示"系统音频捕获功能不可用"**
A: 需要安装soundcard库：`pip install soundcard`

**Q: 音频路由启动失败**
A: 检查设备是否被其他程序占用，或尝试选择其他设备

**Q: MP3播放没有声音**
A: 确认选择了正确的MP3输出设备，检查设备音量设置

**Q: 出现音频反馈/啸叫**
A: 确保输入和输出设备不同，避免音频回路

### 设备建议

**推荐虚拟音频设备:**
- VB-Audio VB-Cable
- VoiceMeeter (Banana/Potato)
- Virtual Audio Cable

## 与GUI版本的区别

| 功能 | GUI版本 | CLI版本 |
|------|---------|---------|
| 设备管理 | 下拉框选择 | 数字编号选择 |
| 状态显示 | 实时进度条 | 文字状态显示 |
| 音量控制 | 滑块调节 | 数值输入 |
| 文件选择 | 文件对话框 | 路径输入 |
| 操作方式 | 鼠标点击 | 键盘输入 |

## 开发信息

- **基于**: GUI版本 audio_router(1).py
- **语言**: Python 3.6+
- **架构**: 多线程音频处理
- **平台**: Windows (主要), Linux/macOS (部分功能)

---

*提示: 首次使用建议先运行 `--list-devices` 查看可用设备*
