class Student:
    """
    学生类
    包含学生的基本信息：ID、姓名、班级、年级
    """
    
    def __init__(self, student_id, name, class_name, grade):
        """
        初始化学生对象
        
        Args:
            student_id (str): 学生ID
            name (str): 学生姓名
            class_name (str): 班级
            grade (str): 年级
        """
        self.student_id = student_id
        self.name = name
        self.class_name = class_name
        self.grade = grade
    
    def __str__(self):
        """
        返回学生信息的字符串表示
        """
        return f"学生ID: {self.student_id}, 姓名: {self.name}, 班级: {self.class_name}, 年级: {self.grade}"
    
    def __repr__(self):
        """
        返回学生对象的官方字符串表示
        """
        return f"Student('{self.student_id}', '{self.name}', '{self.class_name}', '{self.grade}')"
    
    def get_student_info(self):
        """
        获取学生完整信息
        
        Returns:
            dict: 包含学生所有信息的字典
        """
        return {
            'student_id': self.student_id,
            'name': self.name,
            'class_name': self.class_name,
            'grade': self.grade
        }
    
    def update_class(self, new_class):
        """
        更新学生班级
        
        Args:
            new_class (str): 新的班级
        """
        self.class_name = new_class
    
    def update_grade(self, new_grade):
        """
        更新学生年级
        
        Args:
            new_grade (str): 新的年级
        """
        self.grade = new_grade


# 示例使用
if __name__ == "__main__":
    # 创建学生对象
    student1 = Student("2024001", "张三", "一班", "高一")
    student2 = Student("2024002", "李四", "二班", "高二")
    
    # 打印学生信息
    print(student1)
    print(student2)
    
    # 获取学生信息字典
    print("\n学生1详细信息:")
    print(student1.get_student_info())
    
    # 更新学生信息
    student1.update_class("三班")
    student1.update_grade("高二")
    print(f"\n更新后的学生1信息: {student1}")
