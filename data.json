{"errcode": 0, "errmsg": "成功", "data": {"success": true, "msg": "操作成功", "errCode": "200", "list": [{"createDate": "2025-08-28 08:47:50", "updateDate": "2025-08-28 08:48:27", "facCode": "11360", "lineId": 1, "stance": 51, "nextLineId": 1, "nextStance": 1, "hangerId": 126330, "boxCode": "A000000351", "taskNo": 1883, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1402"}, {"createDate": "2025-08-28 08:46:56", "updateDate": "2025-08-28 08:47:45", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 44, "hangerId": 125525, "boxCode": "A000000926", "taskNo": 1882, "flag": 1, "taskNoStr": "callTaskRgv:1401"}, {"createDate": "2025-08-28 08:42:14", "updateDate": "2025-08-28 08:42:55", "facCode": "11360", "lineId": 1, "stance": 44, "nextLineId": 1, "nextStance": 33, "hangerId": 125521, "boxCode": "A000000130", "taskNo": 1877, "flag": 1, "addrCode": "CS-46-01-01@4", "taskNoStr": "callTaskRgvOut:1397"}, {"createDate": "2025-08-28 08:34:00", "updateDate": "2025-08-28 08:34:37", "facCode": "11360", "lineId": 1, "stance": 7, "nextLineId": 1, "nextStance": 4, "hangerId": 126100, "boxCode": "A000000393", "taskNo": 1867, "flag": 1, "taskNoStr": "callTaskRgv:1390"}, {"createDate": "2025-08-28 08:28:16", "updateDate": "2025-08-28 08:29:05", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 44, "hangerId": 125521, "boxCode": "A000000130", "taskNo": 1865, "flag": 1, "taskNoStr": "callTaskRgv:1388"}, {"createDate": "2025-08-28 08:20:50", "updateDate": "2025-08-28 08:22:27", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 32, "hangerId": 126479, "boxCode": "A000000304", "taskNo": 1861, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1387"}, {"createDate": "2025-08-28 08:20:50", "updateDate": "2025-08-28 08:21:48", "facCode": "11360", "lineId": 1, "stance": 44, "nextLineId": 1, "nextStance": 64, "hangerId": 125800, "boxCode": "A000000723", "taskNo": 1860, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1386"}, {"createDate": "2025-08-28 08:20:23", "updateDate": "2025-08-28 08:21:06", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 55, "hangerId": 126425, "boxCode": "A000001018", "taskNo": 1859, "flag": 1, "taskNoStr": "callTaskRgv:1383"}, {"createDate": "2025-08-28 07:27:11", "updateDate": "2025-08-28 07:28:18", "facCode": "11360", "lineId": 1, "stance": 57, "nextLineId": 1, "nextStance": 32, "hangerId": 125446, "boxCode": "A000000775", "taskNo": 1841, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1368"}, {"createDate": "2025-08-28 07:26:56", "updateDate": "2025-08-28 07:27:34", "facCode": "11360", "lineId": 1, "stance": 55, "nextLineId": 1, "nextStance": 33, "hangerId": 125857, "boxCode": "A000001017", "taskNo": 1840, "flag": 1, "addrCode": "CS-46-01-01@4", "taskNoStr": "callTaskRgvOut:1367"}, {"createDate": "2025-08-27 21:01:02", "updateDate": "2025-08-27 21:01:51", "facCode": "11360", "lineId": 1, "stance": 33, "nextLineId": 1, "nextStance": 5, "hangerId": 125477, "boxCode": "A000000242", "taskNo": 1798, "flag": 1, "taskNoStr": "callTaskRgv:893"}, {"createDate": "2025-08-27 20:53:27", "updateDate": "2025-08-27 20:54:16", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 7, "hangerId": 126093, "boxCode": "A000000393", "taskNo": 1796, "flag": 1, "taskNoStr": "callTaskRgv:1328"}, {"createDate": "2025-08-27 20:52:56", "updateDate": "2025-08-27 20:53:45", "facCode": "11360", "lineId": 1, "stance": 33, "nextLineId": 1, "nextStance": 63, "hangerId": 125650, "boxCode": "A000000297", "taskNo": 1795, "flag": 1, "taskNoStr": "pickupTaskRgv:5c78fb4b-24ff-4231-8e78-55ea4f42572b"}, {"createDate": "2025-08-27 20:48:20", "updateDate": "2025-08-27 20:48:58", "facCode": "11360", "lineId": 1, "stance": 63, "nextLineId": 1, "nextStance": 1, "hangerId": 125467, "boxCode": "A000000003", "taskNo": 1792, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1327"}, {"createDate": "2025-08-27 20:09:16", "updateDate": "2025-08-27 20:09:55", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 52, "hangerId": 125685, "boxCode": "A000000764", "taskNo": 1789, "flag": 1, "taskNoStr": "callTaskRgv:1324"}, {"createDate": "2025-08-27 20:02:40", "updateDate": "2025-08-27 20:03:18", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 57, "hangerId": 125447, "boxCode": "A000000775", "taskNo": 1787, "flag": 1, "taskNoStr": "callTaskRgv:1323"}, {"createDate": "2025-08-27 19:45:32", "updateDate": "2025-08-27 19:46:02", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 126479, "boxCode": "A000000304", "taskNo": 1780, "flag": 1, "taskNoStr": "callTaskRgv:1319"}, {"createDate": "2025-08-27 19:39:40", "updateDate": "2025-08-27 19:40:42", "facCode": "11360", "lineId": 1, "stance": 52, "nextLineId": 1, "nextStance": 64, "hangerId": 126425, "boxCode": "A000001018", "taskNo": 1772, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1317"}, {"createDate": "2025-08-27 19:39:27", "updateDate": "2025-08-27 19:40:04", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 1, "hangerId": 126202, "boxCode": "A000000236", "taskNo": 1771, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1316"}, {"createDate": "2025-08-27 17:57:47", "updateDate": "2025-08-27 17:58:28", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 52, "hangerId": 126425, "boxCode": "A000001018", "taskNo": 1754, "flag": 1, "taskNoStr": "callTaskRgv:1304"}, {"createDate": "2025-08-27 17:49:42", "updateDate": "2025-08-27 17:50:12", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 126202, "boxCode": "A000000236", "taskNo": 1748, "flag": 1, "taskNoStr": "callTaskRgv:1302"}, {"createDate": "2025-08-27 17:44:41", "updateDate": "2025-08-27 17:45:10", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 1, "hangerId": 126481, "boxCode": "A000000222", "taskNo": 1742, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1301"}, {"createDate": "2025-08-27 17:39:00", "updateDate": "2025-08-27 17:39:41", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 126481, "boxCode": "A000000222", "taskNo": 1737, "flag": 1, "taskNoStr": "callTaskRgv:1295"}, {"createDate": "2025-08-27 17:27:06", "updateDate": "2025-08-27 17:27:36", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 50, "hangerId": 126554, "boxCode": "A000000610", "taskNo": 1723, "flag": 1, "taskNoStr": "callTaskRgv:1283"}, {"createDate": "2025-08-27 17:26:18", "updateDate": "2025-08-27 17:26:52", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 64, "hangerId": 126149, "boxCode": "A000000734", "taskNo": 1718, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1284"}, {"createDate": "2025-08-27 17:24:24", "updateDate": "2025-08-27 17:25:08", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 48, "hangerId": 125458, "boxCode": "A000000229", "taskNo": 1716, "flag": 1, "taskNoStr": "callTaskRgv:1279"}, {"createDate": "2025-08-27 17:22:30", "updateDate": "2025-08-27 17:23:00", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 126149, "boxCode": "A000000734", "taskNo": 1712, "flag": 1, "taskNoStr": "callTaskRgv:1268"}, {"createDate": "2025-08-27 17:06:43", "updateDate": "2025-08-27 17:07:19", "facCode": "11360", "lineId": 1, "stance": 52, "nextLineId": 1, "nextStance": 1, "hangerId": 126483, "boxCode": "A000000221", "taskNo": 1701, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1274"}, {"createDate": "2025-08-27 17:05:03", "updateDate": "2025-08-27 17:05:46", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 47, "hangerId": 125475, "boxCode": "A000000308", "taskNo": 1699, "flag": 1, "taskNoStr": "callTaskRgv:1271"}, {"createDate": "2025-08-27 17:00:36", "updateDate": "2025-08-27 17:01:16", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 55, "hangerId": 125665, "boxCode": "A000001017", "taskNo": 1695, "flag": 1, "taskNoStr": "callTaskRgv:906"}, {"createDate": "2025-08-27 16:51:31", "updateDate": "2025-08-27 16:52:19", "facCode": "11360", "lineId": 1, "stance": 33, "nextLineId": 1, "nextStance": 52, "hangerId": 126483, "boxCode": "A000000221", "taskNo": 1691, "flag": 1, "taskNoStr": "callTaskRgv:1264"}, {"createDate": "2025-08-27 16:46:14", "updateDate": "2025-08-27 16:46:55", "facCode": "11360", "lineId": 1, "stance": 47, "nextLineId": 4, "nextStance": 0, "hangerId": 125855, "boxCode": "A000000111", "taskNo": 1688, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1102"}, {"createDate": "2025-08-27 16:44:52", "updateDate": "2025-08-27 16:45:43", "facCode": "11360", "lineId": 1, "stance": 33, "nextLineId": 1, "nextStance": 62, "hangerId": 125611, "boxCode": "A000000939", "taskNo": 1687, "flag": 1, "taskNoStr": "pickupTaskRgv:2b924688-5752-4b09-af7b-9d31fc54e713"}, {"createDate": "2025-08-27 16:43:28", "updateDate": "2025-08-27 16:44:11", "facCode": "11360", "lineId": 1, "stance": 48, "nextLineId": 1, "nextStance": 1, "hangerId": 125462, "boxCode": "A000000094", "taskNo": 1686, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1269"}, {"createDate": "2025-08-27 16:35:16", "updateDate": "2025-08-27 16:36:35", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 32, "hangerId": 126170, "boxCode": "A000000821", "taskNo": 1680, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1265"}, {"createDate": "2025-08-27 16:35:12", "updateDate": "2025-08-27 16:35:55", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 64, "hangerId": 125447, "boxCode": "A000000775", "taskNo": 1678, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1263"}, {"createDate": "2025-08-27 16:34:19", "updateDate": "2025-08-27 16:34:52", "facCode": "11360", "lineId": 1, "stance": 52, "nextLineId": 1, "nextStance": 1, "hangerId": 125445, "boxCode": "A000000794", "taskNo": 1676, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1261"}, {"createDate": "2025-08-27 16:21:22", "updateDate": "2025-08-27 16:22:04", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 50, "hangerId": 126170, "boxCode": "A000000821", "taskNo": 1669, "flag": 1, "taskNoStr": "callTaskRgv:1253"}, {"createDate": "2025-08-27 16:10:35", "updateDate": "2025-08-27 16:11:06", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 125447, "boxCode": "A000000775", "taskNo": 1665, "flag": 1, "taskNoStr": "callTaskRgv:1252"}, {"createDate": "2025-08-27 16:05:46", "updateDate": "2025-08-27 16:06:54", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 64, "hangerId": 126412, "boxCode": "A000000926", "taskNo": 1661, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1251"}, {"createDate": "2025-08-27 16:05:30", "updateDate": "2025-08-27 16:06:13", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 1, "hangerId": 125661, "boxCode": "A000000980", "taskNo": 1660, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1250"}, {"createDate": "2025-08-27 16:01:00", "updateDate": "2025-08-27 16:01:34", "facCode": "11360", "lineId": 1, "stance": 62, "nextLineId": 4, "nextStance": 0, "hangerId": 126526, "boxCode": "A000000832", "taskNo": 1658, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1085"}, {"createDate": "2025-08-27 15:58:40", "updateDate": "2025-08-27 15:59:24", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 45, "hangerId": 126589, "boxCode": "A000000183", "taskNo": 1656, "flag": 1, "taskNoStr": "callTaskRgv:1244"}, {"createDate": "2025-08-27 15:55:59", "updateDate": "2025-08-27 15:56:43", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 44, "hangerId": 126051, "boxCode": "A000000723", "taskNo": 1651, "flag": 1, "taskNoStr": "callTaskRgv:1243"}, {"createDate": "2025-08-27 15:50:35", "updateDate": "2025-08-27 15:52:55", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 51, "hangerId": 126331, "boxCode": "A000000351", "taskNo": 1645, "flag": 1, "taskNoStr": "callTaskRgv:1229"}, {"createDate": "2025-08-27 15:50:28", "updateDate": "2025-08-27 15:52:10", "facCode": "11360", "lineId": 1, "stance": 44, "nextLineId": 1, "nextStance": 32, "hangerId": 126544, "boxCode": "A000000189", "taskNo": 1643, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1240"}, {"createDate": "2025-08-27 15:50:27", "updateDate": "2025-08-27 15:51:34", "facCode": "11360", "lineId": 1, "stance": 45, "nextLineId": 1, "nextStance": 33, "hangerId": 126258, "boxCode": "A000000963", "taskNo": 1642, "flag": 1, "addrCode": "CS-46-01-01@4", "taskNoStr": "callTaskRgvOut:1239"}, {"createDate": "2025-08-27 15:10:04", "updateDate": "2025-08-27 15:50:59", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 50, "hangerId": 125525, "boxCode": "A000000926", "taskNo": 1618, "flag": 1, "taskNoStr": "callTaskRgv:1225"}, {"createDate": "2025-08-27 14:37:25", "updateDate": "2025-08-27 15:50:16", "facCode": "11360", "lineId": 1, "stance": 32, "nextLineId": 1, "nextStance": 63, "hangerId": 125467, "boxCode": "A000000003", "taskNo": 1608, "flag": 1, "taskNoStr": "pickupTaskRgv:64ccda85-a547-4f79-a86d-d81b70983614"}, {"createDate": "2025-08-27 14:33:00", "updateDate": "2025-08-27 15:49:28", "facCode": "11360", "lineId": 1, "stance": 33, "nextLineId": 1, "nextStance": 62, "hangerId": 126453, "boxCode": "A000000390", "taskNo": 1605, "flag": 1, "taskNoStr": "pickupTaskRgv:01f61759-635e-4f61-b041-f9ba5d17bc03"}, {"createDate": "2025-08-27 14:31:21", "updateDate": "2025-08-27 15:48:44", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 125661, "boxCode": "A000000980", "taskNo": 1602, "flag": 1, "taskNoStr": "callTaskRgv:1216"}, {"createDate": "2025-08-27 14:20:24", "updateDate": "2025-08-27 15:47:57", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 45, "hangerId": 126482, "boxCode": "A000000963", "taskNo": 1594, "flag": 1, "taskNoStr": "callTaskRgv:1209"}, {"createDate": "2025-08-27 14:17:43", "updateDate": "2025-08-27 14:18:34", "facCode": "11360", "lineId": 1, "stance": 32, "nextLineId": 1, "nextStance": 61, "hangerId": 125625, "boxCode": "A000000303", "taskNo": 1588, "flag": 1, "taskNoStr": "pickupTaskRgv:2eaa91f2-25ea-4ab3-9fae-1294dff2dab3"}, {"createDate": "2025-08-27 14:16:28", "updateDate": "2025-08-27 14:17:17", "facCode": "11360", "lineId": 1, "stance": 33, "nextLineId": 1, "nextStance": 59, "hangerId": 125499, "boxCode": "A000000500", "taskNo": 1587, "flag": 1, "taskNoStr": "pickupTaskRgv:1b902deb-e8cf-40ad-a02a-727a381bfce8"}, {"createDate": "2025-08-27 14:13:58", "updateDate": "2025-08-27 14:14:27", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 1, "hangerId": 125521, "boxCode": "A000000130", "taskNo": 1584, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1207"}, {"createDate": "2025-08-27 14:12:38", "updateDate": "2025-08-27 14:13:08", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 125521, "boxCode": "A000000130", "taskNo": 1583, "flag": 1, "taskNoStr": "callTaskRgv:1205"}, {"createDate": "2025-08-27 14:04:46", "updateDate": "2025-08-27 14:05:18", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 1, "hangerId": 126213, "boxCode": "A000000377", "taskNo": 1578, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1203"}, {"createDate": "2025-08-27 14:03:50", "updateDate": "2025-08-27 14:04:36", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 126213, "boxCode": "A000000377", "taskNo": 1577, "flag": 1, "taskNoStr": "callTaskRgv:1201"}, {"createDate": "2025-08-27 13:39:31", "updateDate": "2025-08-27 13:40:19", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 45, "hangerId": 126487, "boxCode": "A000000624", "taskNo": 1557, "flag": 1, "taskNoStr": "callTaskRgv:1185"}, {"createDate": "2025-08-27 13:39:09", "updateDate": "2025-08-27 13:39:48", "facCode": "11360", "lineId": 1, "stance": 59, "nextLineId": 1, "nextStance": 64, "hangerId": 126610, "boxCode": "A000000622", "taskNo": 1556, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1188"}, {"createDate": "2025-08-27 13:37:24", "updateDate": "2025-08-27 13:37:59", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 44, "hangerId": 126544, "boxCode": "A000000189", "taskNo": 1553, "flag": 1, "taskNoStr": "callTaskRgv:1183"}, {"createDate": "2025-08-27 13:24:58", "updateDate": "2025-08-27 13:25:28", "facCode": "11360", "lineId": 1, "stance": 61, "nextLineId": 1, "nextStance": 1, "hangerId": 126436, "boxCode": "A000000793", "taskNo": 1548, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1182"}, {"createDate": "2025-08-27 13:05:42", "updateDate": "2025-08-27 13:06:25", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 1, "hangerId": 125544, "boxCode": "A000000606", "taskNo": 1541, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1180"}, {"createDate": "2025-08-27 13:04:16", "updateDate": "2025-08-27 13:04:45", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 1, "hangerId": 125585, "boxCode": "A000000966", "taskNo": 1540, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1179"}, {"createDate": "2025-08-27 13:03:17", "updateDate": "2025-08-27 13:04:00", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 50, "hangerId": 125585, "boxCode": "A000000966", "taskNo": 1539, "flag": 1, "taskNoStr": "callTaskRgv:1174"}, {"createDate": "2025-08-27 13:01:20", "updateDate": "2025-08-27 13:02:07", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 125544, "boxCode": "A000000606", "taskNo": 1534, "flag": 1, "taskNoStr": "callTaskRgv:1173"}, {"createDate": "2025-08-27 12:58:00", "updateDate": "2025-08-27 12:58:47", "facCode": "11360", "lineId": 1, "stance": 51, "nextLineId": 1, "nextStance": 32, "hangerId": 126482, "boxCode": "A000000963", "taskNo": 1528, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1172"}, {"createDate": "2025-08-27 12:56:43", "updateDate": "2025-08-27 12:58:06", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 64, "hangerId": 125561, "boxCode": "A000000522", "taskNo": 1527, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1171"}, {"createDate": "2025-08-27 12:56:42", "updateDate": "2025-08-27 12:57:20", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 1, "hangerId": 126487, "boxCode": "A000000624", "taskNo": 1526, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1170"}, {"createDate": "2025-08-27 12:45:29", "updateDate": "2025-08-27 12:46:13", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 51, "hangerId": 126482, "boxCode": "A000000963", "taskNo": 1523, "flag": 1, "taskNoStr": "callTaskRgv:1166"}, {"createDate": "2025-08-27 12:43:27", "updateDate": "2025-08-27 12:44:13", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 43, "hangerId": 126006, "boxCode": "A000000197", "taskNo": 1520, "flag": 1, "taskNoStr": "callTaskRgv:1165"}, {"createDate": "2025-08-27 12:09:16", "updateDate": "2025-08-27 12:10:00", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 50, "hangerId": 125561, "boxCode": "A000000522", "taskNo": 1512, "flag": 1, "taskNoStr": "callTaskRgv:1159"}, {"createDate": "2025-08-27 12:04:48", "updateDate": "2025-08-27 12:05:36", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 126487, "boxCode": "A000000624", "taskNo": 1508, "flag": 1, "taskNoStr": "callTaskRgv:1158"}, {"createDate": "2025-08-27 12:01:06", "updateDate": "2025-08-27 12:02:45", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 32, "hangerId": 125461, "boxCode": "A000000776", "taskNo": 1502, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1157"}, {"createDate": "2025-08-27 12:01:02", "updateDate": "2025-08-27 12:02:05", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 64, "hangerId": 126421, "boxCode": "A000000777", "taskNo": 1501, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1156"}, {"createDate": "2025-08-27 12:00:49", "updateDate": "2025-08-27 12:01:18", "facCode": "11360", "lineId": 1, "stance": 51, "nextLineId": 1, "nextStance": 1, "hangerId": 125845, "boxCode": "A000000931", "taskNo": 1500, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1155"}, {"createDate": "2025-08-27 11:57:01", "updateDate": "2025-08-27 11:57:44", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 51, "hangerId": 125845, "boxCode": "A000000931", "taskNo": 1499, "flag": 1, "taskNoStr": "callTaskRgv:1150"}, {"createDate": "2025-08-27 11:43:49", "updateDate": "2025-08-27 11:44:23", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 50, "hangerId": 126421, "boxCode": "A000000777", "taskNo": 1493, "flag": 1, "taskNoStr": "callTaskRgv:1149"}, {"createDate": "2025-08-27 11:40:48", "updateDate": "2025-08-27 11:41:29", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 64, "hangerId": 126467, "boxCode": "A000000468", "taskNo": 1487, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1147"}, {"createDate": "2025-08-27 11:40:11", "updateDate": "2025-08-27 11:40:43", "facCode": "11360", "lineId": 1, "stance": 51, "nextLineId": 1, "nextStance": 1, "hangerId": 126095, "boxCode": "A000000574", "taskNo": 1485, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1145"}, {"createDate": "2025-08-27 11:29:08", "updateDate": "2025-08-27 11:29:50", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 51, "hangerId": 126095, "boxCode": "A000000574", "taskNo": 1469, "flag": 1, "taskNoStr": "callTaskRgv:1130"}, {"createDate": "2025-08-27 11:26:26", "updateDate": "2025-08-27 11:26:59", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 50, "hangerId": 126467, "boxCode": "A000000468", "taskNo": 1466, "flag": 1, "taskNoStr": "callTaskRgv:1129"}, {"createDate": "2025-08-27 11:22:23", "updateDate": "2025-08-27 11:23:29", "facCode": "11360", "lineId": 1, "stance": 51, "nextLineId": 1, "nextStance": 64, "hangerId": 126116, "boxCode": "A000000307", "taskNo": 1461, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1128"}, {"createDate": "2025-08-27 11:22:16", "updateDate": "2025-08-27 11:22:50", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 1, "hangerId": 126092, "boxCode": "A000000934", "taskNo": 1460, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1127"}, {"createDate": "2025-08-27 11:16:15", "updateDate": "2025-08-27 11:17:04", "facCode": "11360", "lineId": 1, "stance": 1, "nextLineId": 1, "nextStance": 49, "hangerId": 125461, "boxCode": "A000000776", "taskNo": 1453, "flag": 1, "taskNoStr": "callTaskRgv:1121"}, {"createDate": "2025-08-27 11:15:38", "updateDate": "2025-08-27 11:16:21", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 51, "hangerId": 126116, "boxCode": "A000000307", "taskNo": 1451, "flag": 1, "taskNoStr": "callTaskRgv:1122"}, {"createDate": "2025-08-27 11:14:06", "updateDate": "2025-08-27 11:14:38", "facCode": "11360", "lineId": 1, "stance": 33, "nextLineId": 1, "nextStance": 50, "hangerId": 126092, "boxCode": "A000000934", "taskNo": 1449, "flag": 1, "taskNoStr": "callTaskRgv:1118"}, {"createDate": "2025-08-27 11:08:15", "updateDate": "2025-08-27 11:09:56", "facCode": "11360", "lineId": 1, "stance": 51, "nextLineId": 1, "nextStance": 32, "hangerId": 126091, "boxCode": "A000000422", "taskNo": 1437, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1117"}, {"createDate": "2025-08-27 11:07:59", "updateDate": "2025-08-27 11:09:15", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 1, "nextStance": 64, "hangerId": 125645, "boxCode": "A000000244", "taskNo": 1436, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1116"}, {"createDate": "2025-08-27 11:07:57", "updateDate": "2025-08-27 11:08:33", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 1, "hangerId": 125558, "boxCode": "A000000378", "taskNo": 1435, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1115"}, {"createDate": "2025-08-27 10:57:23", "updateDate": "2025-08-27 11:06:44", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 51, "hangerId": 126091, "boxCode": "A000000422", "taskNo": 1431, "flag": 1, "taskNoStr": "callTaskRgv:1109"}, {"createDate": "2025-08-27 10:45:55", "updateDate": "2025-08-27 11:06:15", "facCode": "11360", "lineId": 1, "stance": 63, "nextLineId": 1, "nextStance": 1, "hangerId": 126529, "boxCode": "A000000964", "taskNo": 1427, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1110"}, {"createDate": "2025-08-27 10:43:45", "updateDate": "2025-08-27 10:44:13", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 50, "hangerId": 125558, "boxCode": "A000000378", "taskNo": 1423, "flag": 1, "taskNoStr": "callTaskRgv:1106"}, {"createDate": "2025-08-27 10:41:51", "updateDate": "2025-08-27 10:42:31", "facCode": "11360", "lineId": 1, "stance": 51, "nextLineId": 1, "nextStance": 1, "hangerId": 125653, "boxCode": "A000000571", "taskNo": 1420, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1107"}, {"createDate": "2025-08-27 10:38:53", "updateDate": "2025-08-27 10:41:17", "facCode": "11360", "lineId": 1, "stance": 50, "nextLineId": 1, "nextStance": 33, "hangerId": 125549, "boxCode": "A000000126", "taskNo": 1414, "flag": 1, "addrCode": "CS-46-01-01@4", "taskNoStr": "callTaskRgvOut:1101"}, {"createDate": "2025-08-27 10:38:51", "updateDate": "2025-08-27 10:40:36", "facCode": "11360", "lineId": 1, "stance": 9, "nextLineId": 1, "nextStance": 32, "hangerId": 125635, "boxCode": "A000000912", "taskNo": 1412, "flag": 1, "addrCode": "CS-45-01-01@4", "taskNoStr": "callTaskRgvOut:1099"}, {"createDate": "2025-08-27 10:38:50", "updateDate": "2025-08-27 10:39:59", "facCode": "11360", "lineId": 1, "stance": 8, "nextLineId": 1, "nextStance": 64, "hangerId": 125636, "boxCode": "A000000584", "taskNo": 1411, "flag": 1, "addrCode": "CS-42-01-01@4", "taskNoStr": "callTaskRgvOut:1098"}, {"createDate": "2025-08-27 10:38:50", "updateDate": "2025-08-27 10:39:25", "facCode": "11360", "lineId": 1, "stance": 7, "nextLineId": 1, "nextStance": 1, "hangerId": 125547, "boxCode": "A000000181", "taskNo": 1410, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:1097"}, {"createDate": "2025-08-27 10:20:21", "updateDate": "2025-08-27 10:20:52", "facCode": "11360", "lineId": 1, "stance": 64, "nextLineId": 1, "nextStance": 49, "hangerId": 125645, "boxCode": "A000000244", "taskNo": 1407, "flag": 1, "taskNoStr": "callTaskRgv:1094"}, {"createDate": "2025-08-27 10:16:44", "updateDate": "2025-08-27 10:17:30", "facCode": "11360", "lineId": 1, "stance": 49, "nextLineId": 2, "nextStance": 0, "hangerId": 125838, "boxCode": "A000000894", "taskNo": 1404, "flag": 1, "addrCode": "CS-41-01-01@4", "taskNoStr": "callTaskRgvOut:798"}]}}