package com.example.mapper;

import com.example.entity.BoxFlowLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 箱子流转日志Mapper接口
 */
@Mapper
public interface BoxFlowLogMapper {

    /**
     * 插入箱子流转日志
     * @param boxFlowLog 箱子流转日志对象
     * @return 影响行数
     */
    int insert(BoxFlowLog boxFlowLog);

    /**
     * 批量插入箱子流转日志
     * @param list 箱子流转日志列表
     * @return 影响行数
     */
    int insertBatch(List<BoxFlowLog> list);

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 箱子流转日志对象
     */
    BoxFlowLog selectById(@Param("id") Long id);

    /**
     * 根据箱子编号查询
     * @param boxNo 箱子编号
     * @return 箱子流转日志列表
     */
    List<BoxFlowLog> selectByBoxNo(@Param("boxNo") String boxNo);

    /**
     * 根据箱子唯一标识查询
     * @param boxUnid 箱子唯一标识
     * @return 箱子流转日志对象
     */
    BoxFlowLog selectByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 查询所有记录（包含逻辑删除）
     * @return 箱子流转日志列表
     */
    List<BoxFlowLog> selectAll();

    /**
     * 查询所有有效记录（排除逻辑删除）
     * @return 箱子流转日志列表
     */
    List<BoxFlowLog> selectAllValid();

    /**
     * 查询所有逻辑删除的记录
     * @return 箱子流转日志列表
     */
    List<BoxFlowLog> selectAllDeleted();

    /**
     * 根据boxUnid查询有效记录（排除逻辑删除）
     * @param boxUnid 箱子唯一标识
     * @return 箱子流转日志对象
     */
    BoxFlowLog selectValidByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 更新箱子流转日志
     * @param boxFlowLog 箱子流转日志对象
     * @return 影响行数
     */
    int update(BoxFlowLog boxFlowLog);

    /**
     * 根据boxUnid更新箱子流转日志
     * @param boxFlowLog 箱子流转日志对象
     * @return 影响行数
     */
    int updateByBoxUnid(BoxFlowLog boxFlowLog);

    /**
     * 逻辑删除（根据boxUnid） - 将数值字段设置为负数
     * @param boxUnid 箱子唯一标识
     * @return 影响行数
     */
    int logicalDeleteByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 逻辑删除（根据ID） - 将数值字段设置为负数
     * @param id 主键ID
     * @return 影响行数
     */
    int logicalDeleteById(@Param("id") Long id);

    /**
     * 恢复逻辑删除（根据boxUnid） - 将负数字段恢复为正数
     * @param boxUnid 箱子唯一标识
     * @return 影响行数
     */
    int restoreByBoxUnid(@Param("boxUnid") String boxUnid);

    /**
     * 恢复逻辑删除（根据ID） - 将负数字段恢复为正数
     * @param id 主键ID
     * @return 影响行数
     */
    int restoreById(@Param("id") Long id);

    /**
     * 根据ID删除
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
