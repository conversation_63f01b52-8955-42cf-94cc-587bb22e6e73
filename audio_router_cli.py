#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频路由工具 - 命令行版本
VB-Cable伴侣工具 (增强版)
"""

import os
import sys
import time
import threading
import wave
import numpy as np
import pyaudio
from pydub import AudioSegment
import argparse
import signal
import json
from datetime import datetime

# 尝试导入可选依赖
try:
    import soundcard as sc
    SOUNDCARD_AVAILABLE = True
except ImportError:
    SOUNDCARD_AVAILABLE = False

try:
    import pycaw
    from pycaw.pycaw import AudioUtilities, AudioSession
    PYCAW_AVAILABLE = True
except ImportError:
    PYCAW_AVAILABLE = False

class Colors:
    """ANSI颜色代码"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class AudioRouterCLI:
    def __init__(self):
        """初始化音频路由器"""
        self.audio = pyaudio.PyAudio()
        
        # 音频路由相关变量
        self.routing_stream_in = None
        self.routing_stream_out = None
        self.routing_thread = None
        self.is_routing = False
        self.routing_volume = 1.0
        self.routing_muted = False
        
        # 系统音频捕获相关
        self.system_audio_mode = False
        self.system_mic = None
        
        # MP3播放相关变量
        self.current_mp3 = None
        self.mp3_paused = False
        self.mp3_volume = 1.0
        self.mp3_stream = None
        self.mp3_thread = None
        self.mp3_playing = False
        self.mp3_audio_data = None
        
        # 音频设备列表
        self.input_devices = []
        self.output_devices = []
        
        # 选中的设备
        self.selected_input_device = None
        self.selected_output_device = None
        self.selected_mp3_output_device = None
        
        # 音频隔离模式
        self.isolation_mode = False
        
        # 状态显示
        self.status_thread = None
        self.show_status = False
        
        # 信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        
        # 初始化
        self.refresh_devices()
        
    def signal_handler(self, signum, frame):
        """处理Ctrl+C信号"""
        print(f"\n{Colors.WARNING}收到退出信号，正在清理资源...{Colors.ENDC}")
        self.cleanup()
        sys.exit(0)
    
    def print_colored(self, text, color=Colors.ENDC):
        """打印彩色文本"""
        print(f"{color}{text}{Colors.ENDC}")
    
    def print_header(self, text):
        """打印标题"""
        print(f"\n{Colors.BOLD}{Colors.HEADER}{'='*60}{Colors.ENDC}")
        print(f"{Colors.BOLD}{Colors.HEADER}{text.center(60)}{Colors.ENDC}")
        print(f"{Colors.BOLD}{Colors.HEADER}{'='*60}{Colors.ENDC}")
    
    def print_status(self, text):
        """打印状态信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{Colors.OKCYAN}[{timestamp}]{Colors.ENDC} {text}")
    
    def print_error(self, text):
        """打印错误信息"""
        print(f"{Colors.FAIL}错误: {text}{Colors.ENDC}")
    
    def print_warning(self, text):
        """打印警告信息"""
        print(f"{Colors.WARNING}警告: {text}{Colors.ENDC}")
    
    def print_success(self, text):
        """打印成功信息"""
        print(f"{Colors.OKGREEN}✓ {text}{Colors.ENDC}")
    
    def refresh_devices(self):
        """刷新音频设备列表"""
        try:
            self.input_devices = []
            self.output_devices = []
            
            device_count = self.audio.get_device_count()
            for i in range(device_count):
                try:
                    device_info = self.audio.get_device_info_by_index(i)
                    device_name = device_info['name']
                    
                    # 跳过一些可能有问题的设备类型
                    if any(skip_word in device_name.lower() for skip_word in 
                           ['microsoft sound mapper', 'primary sound', 'communications']):
                        continue
                    
                    # 创建设备显示名称
                    display_name = f"{device_name}"
                    if device_info.get('hostApi', 0) != -1:
                        try:
                            host_api_info = self.audio.get_host_api_info_by_index(device_info['hostApi'])
                            api_name = host_api_info['name']
                            if api_name not in device_name:
                                display_name += f" ({api_name})"
                        except:
                            pass
                    
                    # 添加输入设备
                    if device_info['maxInputChannels'] > 0:
                        try:
                            test_stream = self.audio.open(
                                format=pyaudio.paInt16,
                                channels=1,
                                rate=int(device_info['defaultSampleRate']) or 44100,
                                input=True,
                                input_device_index=i,
                                frames_per_buffer=1024
                            )
                            test_stream.close()
                            self.input_devices.append((i, display_name))
                        except:
                            self.input_devices.append((i, display_name + " [可能不可用]"))
                    
                    # 添加输出设备
                    if device_info['maxOutputChannels'] > 0:
                        try:
                            test_stream = self.audio.open(
                                format=pyaudio.paInt16,
                                channels=1,
                                rate=int(device_info['defaultSampleRate']) or 44100,
                                output=True,
                                output_device_index=i,
                                frames_per_buffer=1024
                            )
                            test_stream.close()
                            self.output_devices.append((i, display_name))
                        except:
                            self.output_devices.append((i, display_name + " [可能不可用]"))
                            
                except Exception as e:
                    continue
            
            # 自动选择VB-Cable设备
            self.auto_select_vb_cable()
            
        except Exception as e:
            self.print_error(f"刷新设备失败: {str(e)}")
    
    def auto_select_vb_cable(self):
        """自动选择VB-Cable设备"""
        vb_cable_found = False
        
        # 查找VB-Cable输入设备
        for i, (device_id, name) in enumerate(self.input_devices):
            if any(vb_word in name.upper() for vb_word in ["CABLE OUTPUT", "VB-CABLE OUTPUT", "CABLE OUT"]):
                self.selected_input_device = device_id
                vb_cable_found = True
                break
        
        # 查找VB-Cable输出设备
        for i, (device_id, name) in enumerate(self.output_devices):
            if any(vb_word in name.upper() for vb_word in ["CABLE INPUT", "VB-CABLE INPUT", "CABLE IN"]):
                self.selected_output_device = device_id
                self.selected_mp3_output_device = device_id
                vb_cable_found = True
                break
        
        # 如果没有找到VB-Cable，选择默认设备
        if not vb_cable_found:
            if self.input_devices:
                self.selected_input_device = self.input_devices[0][0]
            if self.output_devices:
                self.selected_output_device = self.output_devices[0][0]
                self.selected_mp3_output_device = self.output_devices[0][0]
        
        return vb_cable_found
    
    def show_devices(self):
        """显示音频设备列表"""
        self.print_header("音频设备列表")
        
        print(f"\n{Colors.BOLD}输入设备:{Colors.ENDC}")
        for i, (device_id, name) in enumerate(self.input_devices):
            marker = " ← 当前选择" if device_id == self.selected_input_device else ""
            print(f"  {i+1:2d}. [{device_id:2d}] {name}{Colors.OKGREEN}{marker}{Colors.ENDC}")
        
        print(f"\n{Colors.BOLD}输出设备:{Colors.ENDC}")
        for i, (device_id, name) in enumerate(self.output_devices):
            marker = " ← 当前选择" if device_id == self.selected_output_device else ""
            mp3_marker = " ← MP3输出" if device_id == self.selected_mp3_output_device else ""
            print(f"  {i+1:2d}. [{device_id:2d}] {name}{Colors.OKGREEN}{marker}{mp3_marker}{Colors.ENDC}")
        
        print(f"\n检测到 {len(self.input_devices)} 个输入设备，{len(self.output_devices)} 个输出设备")
        
        # 显示系统音频捕获支持状态
        if SOUNDCARD_AVAILABLE:
            self.print_success("系统音频捕获功能可用 (soundcard库已安装)")
        else:
            self.print_warning("系统音频捕获功能不可用 (需要安装: pip install soundcard)")

    def select_input_device(self):
        """选择输入设备"""
        if not self.input_devices:
            self.print_error("没有可用的输入设备")
            return False

        print(f"\n{Colors.BOLD}选择输入设备:{Colors.ENDC}")
        for i, (device_id, name) in enumerate(self.input_devices):
            marker = " ← 当前" if device_id == self.selected_input_device else ""
            print(f"  {i+1}. {name}{Colors.OKGREEN}{marker}{Colors.ENDC}")

        try:
            choice = input(f"\n请输入设备编号 (1-{len(self.input_devices)}) [回车保持当前]: ").strip()
            if choice == "":
                return True

            choice = int(choice)
            if 1 <= choice <= len(self.input_devices):
                self.selected_input_device = self.input_devices[choice-1][0]
                device_name = self.input_devices[choice-1][1]
                self.print_success(f"已选择输入设备: {device_name}")
                return True
            else:
                self.print_error("无效的设备编号")
                return False
        except ValueError:
            self.print_error("请输入有效的数字")
            return False

    def select_output_device(self):
        """选择输出设备"""
        if not self.output_devices:
            self.print_error("没有可用的输出设备")
            return False

        print(f"\n{Colors.BOLD}选择输出设备:{Colors.ENDC}")
        for i, (device_id, name) in enumerate(self.output_devices):
            marker = " ← 当前" if device_id == self.selected_output_device else ""
            print(f"  {i+1}. {name}{Colors.OKGREEN}{marker}{Colors.ENDC}")

        try:
            choice = input(f"\n请输入设备编号 (1-{len(self.output_devices)}) [回车保持当前]: ").strip()
            if choice == "":
                return True

            choice = int(choice)
            if 1 <= choice <= len(self.output_devices):
                self.selected_output_device = self.output_devices[choice-1][0]
                device_name = self.output_devices[choice-1][1]
                self.print_success(f"已选择输出设备: {device_name}")
                return True
            else:
                self.print_error("无效的设备编号")
                return False
        except ValueError:
            self.print_error("请输入有效的数字")
            return False

    def select_mp3_output_device(self):
        """选择MP3输出设备"""
        if not self.output_devices:
            self.print_error("没有可用的输出设备")
            return False

        print(f"\n{Colors.BOLD}选择MP3输出设备:{Colors.ENDC}")
        for i, (device_id, name) in enumerate(self.output_devices):
            marker = " ← 当前MP3输出" if device_id == self.selected_mp3_output_device else ""
            print(f"  {i+1}. {name}{Colors.OKGREEN}{marker}{Colors.ENDC}")

        try:
            choice = input(f"\n请输入设备编号 (1-{len(self.output_devices)}) [回车保持当前]: ").strip()
            if choice == "":
                return True

            choice = int(choice)
            if 1 <= choice <= len(self.output_devices):
                self.selected_mp3_output_device = self.output_devices[choice-1][0]
                device_name = self.output_devices[choice-1][1]
                self.print_success(f"已选择MP3输出设备: {device_name}")
                return True
            else:
                self.print_error("无效的设备编号")
                return False
        except ValueError:
            self.print_error("请输入有效的数字")
            return False

    def show_current_status(self):
        """显示当前状态"""
        self.print_header("当前状态")

        # 设备状态
        input_name = "未选择"
        output_name = "未选择"
        mp3_output_name = "未选择"

        for device_id, name in self.input_devices:
            if device_id == self.selected_input_device:
                input_name = name
                break

        for device_id, name in self.output_devices:
            if device_id == self.selected_output_device:
                output_name = name
            if device_id == self.selected_mp3_output_device:
                mp3_output_name = name

        print(f"输入设备: {Colors.OKCYAN}{input_name}{Colors.ENDC}")
        print(f"输出设备: {Colors.OKCYAN}{output_name}{Colors.ENDC}")
        print(f"MP3输出设备: {Colors.OKCYAN}{mp3_output_name}{Colors.ENDC}")

        # 路由状态
        routing_status = "运行中" if self.is_routing else "已停止"
        routing_color = Colors.OKGREEN if self.is_routing else Colors.FAIL
        print(f"音频路由: {routing_color}{routing_status}{Colors.ENDC}")

        if self.is_routing:
            volume_percent = int(self.routing_volume * 100)
            mute_status = " (静音)" if self.routing_muted else ""
            print(f"路由音量: {Colors.OKCYAN}{volume_percent}%{mute_status}{Colors.ENDC}")

        # 系统音频模式
        sys_audio_status = "启用" if self.system_audio_mode else "禁用"
        sys_audio_color = Colors.OKGREEN if self.system_audio_mode else Colors.FAIL
        print(f"系统音频捕获: {sys_audio_color}{sys_audio_status}{Colors.ENDC}")

        # 音频隔离模式
        isolation_status = "启用" if self.isolation_mode else "禁用"
        isolation_color = Colors.OKGREEN if self.isolation_mode else Colors.FAIL
        print(f"音频隔离模式: {isolation_color}{isolation_status}{Colors.ENDC}")

        # MP3状态
        if self.current_mp3:
            mp3_filename = os.path.basename(self.current_mp3)
            print(f"MP3文件: {Colors.OKCYAN}{mp3_filename}{Colors.ENDC}")

            mp3_status = "播放中" if self.mp3_playing and not self.mp3_paused else \
                        "暂停" if self.mp3_playing and self.mp3_paused else "已停止"
            mp3_color = Colors.OKGREEN if self.mp3_playing and not self.mp3_paused else \
                       Colors.WARNING if self.mp3_paused else Colors.FAIL
            print(f"MP3状态: {mp3_color}{mp3_status}{Colors.ENDC}")

            if self.mp3_playing:
                mp3_volume_percent = int(self.mp3_volume * 100)
                print(f"MP3音量: {Colors.OKCYAN}{mp3_volume_percent}%{Colors.ENDC}")
        else:
            print(f"MP3文件: {Colors.FAIL}未选择{Colors.ENDC}")

    def show_main_menu(self):
        """显示主菜单"""
        self.print_header("音频路由工具 - 命令行版")

        print(f"\n{Colors.BOLD}主菜单:{Colors.ENDC}")
        print("  1. 显示音频设备")
        print("  2. 选择输入设备")
        print("  3. 选择输出设备")
        print("  4. 选择MP3输出设备")
        print("  5. 显示当前状态")
        print("  6. 音频路由控制")
        print("  7. MP3播放控制")
        print("  8. 系统音频捕获设置")
        print("  9. 音频隔离模式设置")
        print("  0. 退出程序")

        return input(f"\n{Colors.BOLD}请选择操作 (0-9): {Colors.ENDC}").strip()

    def audio_routing_menu(self):
        """音频路由控制菜单"""
        while True:
            self.print_header("音频路由控制")

            status = "运行中" if self.is_routing else "已停止"
            status_color = Colors.OKGREEN if self.is_routing else Colors.FAIL
            print(f"当前状态: {status_color}{status}{Colors.ENDC}")

            if self.is_routing:
                volume_percent = int(self.routing_volume * 100)
                mute_status = " (静音)" if self.routing_muted else ""
                print(f"音量: {Colors.OKCYAN}{volume_percent}%{mute_status}{Colors.ENDC}")

            print(f"\n{Colors.BOLD}路由控制:{Colors.ENDC}")
            if not self.is_routing:
                print("  1. 开始路由")
            else:
                print("  1. 停止路由")

            if self.is_routing:
                print("  2. 调整音量")
                mute_text = "取消静音" if self.routing_muted else "静音"
                print(f"  3. {mute_text}")

            print("  0. 返回主菜单")

            choice = input(f"\n{Colors.BOLD}请选择操作: {Colors.ENDC}").strip()

            if choice == "0":
                break
            elif choice == "1":
                if not self.is_routing:
                    self.start_routing()
                else:
                    self.stop_routing()
            elif choice == "2" and self.is_routing:
                self.adjust_routing_volume()
            elif choice == "3" and self.is_routing:
                self.toggle_routing_mute()
            else:
                self.print_error("无效的选择")

            input(f"\n{Colors.OKCYAN}按回车键继续...{Colors.ENDC}")

    def start_routing(self):
        """开始音频路由"""
        if self.system_audio_mode:
            self.start_system_audio_capture()
            return

        if self.isolation_mode:
            self.start_isolated_audio_routing()
            return

        if self.selected_input_device is None or self.selected_output_device is None:
            self.print_error("请先选择输入和输出设备")
            return

        if self.selected_input_device == self.selected_output_device:
            self.print_error("输入和输出设备不能相同，这会造成音频反馈")
            return

        try:
            # 获取设备信息
            input_info = self.audio.get_device_info_by_index(self.selected_input_device)
            output_info = self.audio.get_device_info_by_index(self.selected_output_device)

            # 确定音频参数
            CHUNK = 1024
            FORMAT = pyaudio.paInt16

            input_channels = int(input_info['maxInputChannels'])
            output_channels = int(output_info['maxOutputChannels'])
            CHANNELS = min(input_channels, output_channels, 2)

            if CHANNELS == 0:
                self.print_error("选择的设备不支持所需的音频通道")
                return

            input_rate = int(input_info['defaultSampleRate'])
            output_rate = int(output_info['defaultSampleRate'])

            common_rates = [44100, 48000, 22050, 16000, 8000]
            RATE = input_rate

            if input_rate != output_rate:
                for rate in common_rates:
                    if rate <= max(input_rate, output_rate):
                        RATE = rate
                        break

            if RATE < 8000:
                RATE = 44100

            self.print_status(f"使用参数: 采样率={RATE}Hz, 通道={CHANNELS}, 格式=Int16")

            # 打开音频流
            try:
                self.routing_stream_in = self.audio.open(
                    format=FORMAT,
                    channels=CHANNELS,
                    rate=RATE,
                    input=True,
                    input_device_index=self.selected_input_device,
                    frames_per_buffer=CHUNK
                )
            except Exception as e:
                if CHANNELS == 2:
                    CHANNELS = 1
                    self.routing_stream_in = self.audio.open(
                        format=FORMAT,
                        channels=CHANNELS,
                        rate=RATE,
                        input=True,
                        input_device_index=self.selected_input_device,
                        frames_per_buffer=CHUNK
                    )
                else:
                    raise e

            try:
                self.routing_stream_out = self.audio.open(
                    format=FORMAT,
                    channels=CHANNELS,
                    rate=RATE,
                    output=True,
                    output_device_index=self.selected_output_device,
                    frames_per_buffer=CHUNK
                )
            except Exception as e:
                if self.routing_stream_in:
                    self.routing_stream_in.close()
                    self.routing_stream_in = None
                raise e

            # 开始路由线程
            self.is_routing = True
            self.routing_thread = threading.Thread(target=self.routing_worker)
            self.routing_thread.daemon = True
            self.routing_thread.start()

            self.print_success("音频路由已启动")

        except Exception as e:
            self.print_error(f"启动音频路由失败: {str(e)}")
            self.stop_routing()

    def stop_routing(self):
        """停止音频路由"""
        if self.system_audio_mode:
            self.stop_system_audio_capture()
            return

        self.is_routing = False

        if self.routing_thread:
            self.routing_thread.join(timeout=1.0)

        if self.routing_stream_in:
            self.routing_stream_in.stop_stream()
            self.routing_stream_in.close()
            self.routing_stream_in = None

        if self.routing_stream_out:
            self.routing_stream_out.stop_stream()
            self.routing_stream_out.close()
            self.routing_stream_out = None

        self.print_success("音频路由已停止")

    def routing_worker(self):
        """音频路由工作线程"""
        try:
            while self.is_routing:
                data = self.routing_stream_in.read(1024, exception_on_overflow=False)

                if not self.routing_muted:
                    if self.routing_volume != 1.0 and len(data) > 0:
                        try:
                            audio_data = np.frombuffer(data, dtype=np.int16)
                            if len(audio_data) > 0:
                                scaled_data = audio_data.astype(np.float64) * self.routing_volume
                                scaled_data = np.clip(scaled_data, -32768, 32767)
                                audio_data = scaled_data.astype(np.int16)
                                data = audio_data.tobytes()
                        except (ValueError, MemoryError):
                            pass

                    self.routing_stream_out.write(data)
                else:
                    silence = b'\x00' * len(data)
                    self.routing_stream_out.write(silence)

        except Exception as e:
            print(f"\n{Colors.FAIL}音频路由出错: {str(e)}{Colors.ENDC}")
            self.is_routing = False

    def adjust_routing_volume(self):
        """调整路由音量"""
        current_volume = int(self.routing_volume * 100)
        print(f"\n当前音量: {Colors.OKCYAN}{current_volume}%{Colors.ENDC}")

        try:
            new_volume = input("请输入新音量 (0-100) [回车保持当前]: ").strip()
            if new_volume == "":
                return

            volume = int(new_volume)
            if 0 <= volume <= 100:
                self.routing_volume = volume / 100.0
                self.print_success(f"音量已设置为 {volume}%")
            else:
                self.print_error("音量必须在0-100之间")
        except ValueError:
            self.print_error("请输入有效的数字")

    def toggle_routing_mute(self):
        """切换路由静音状态"""
        self.routing_muted = not self.routing_muted
        status = "已静音" if self.routing_muted else "已取消静音"
        self.print_success(f"路由音频{status}")

    def mp3_control_menu(self):
        """MP3播放控制菜单"""
        while True:
            self.print_header("MP3播放控制")

            if self.current_mp3:
                filename = os.path.basename(self.current_mp3)
                print(f"当前文件: {Colors.OKCYAN}{filename}{Colors.ENDC}")

                status = "播放中" if self.mp3_playing and not self.mp3_paused else \
                        "暂停" if self.mp3_playing and self.mp3_paused else "已停止"
                status_color = Colors.OKGREEN if self.mp3_playing and not self.mp3_paused else \
                              Colors.WARNING if self.mp3_paused else Colors.FAIL
                print(f"状态: {status_color}{status}{Colors.ENDC}")

                if self.mp3_playing:
                    volume_percent = int(self.mp3_volume * 100)
                    print(f"音量: {Colors.OKCYAN}{volume_percent}%{Colors.ENDC}")
            else:
                print(f"当前文件: {Colors.FAIL}未选择{Colors.ENDC}")

            print(f"\n{Colors.BOLD}MP3控制:{Colors.ENDC}")
            print("  1. 选择MP3文件")

            if self.current_mp3:
                if not self.mp3_playing:
                    print("  2. 播放")
                elif self.mp3_paused:
                    print("  2. 继续播放")
                else:
                    print("  2. 暂停")

                print("  3. 停止")
                print("  4. 调整音量")
                print("  5. 静音/取消静音")

            print("  0. 返回主菜单")

            choice = input(f"\n{Colors.BOLD}请选择操作: {Colors.ENDC}").strip()

            if choice == "0":
                break
            elif choice == "1":
                self.select_mp3_file()
            elif choice == "2" and self.current_mp3:
                self.play_mp3()
            elif choice == "3" and self.current_mp3:
                self.stop_mp3()
            elif choice == "4" and self.current_mp3:
                self.adjust_mp3_volume()
            elif choice == "5" and self.current_mp3:
                self.toggle_mp3_mute()
            else:
                self.print_error("无效的选择")

            input(f"\n{Colors.OKCYAN}按回车键继续...{Colors.ENDC}")

    def select_mp3_file(self):
        """选择MP3文件"""
        print(f"\n{Colors.BOLD}选择MP3文件:{Colors.ENDC}")
        file_path = input("请输入MP3文件路径: ").strip().strip('"')

        if not file_path:
            return

        if not os.path.exists(file_path):
            self.print_error("文件不存在")
            return

        if not file_path.lower().endswith('.mp3'):
            self.print_warning("警告: 文件扩展名不是.mp3，可能无法正常播放")

        self.current_mp3 = file_path
        filename = os.path.basename(file_path)
        self.print_success(f"已选择文件: {filename}")

    def play_mp3(self):
        """播放MP3文件"""
        if not self.current_mp3:
            self.print_error("请先选择MP3文件")
            return

        try:
            if self.mp3_playing and self.mp3_paused:
                # 恢复播放
                self.mp3_paused = False
                self.print_success("MP3播放已恢复")
            elif self.mp3_playing:
                # 暂停播放
                self.mp3_paused = True
                self.print_success("MP3播放已暂停")
            else:
                # 开始播放
                self.start_mp3_playback()

        except Exception as e:
            self.print_error(f"播放MP3失败: {str(e)}")

    def start_mp3_playback(self):
        """开始MP3播放"""
        try:
            if self.selected_mp3_output_device is None:
                self.print_error("请先选择MP3输出设备")
                return

            # 获取输出设备信息
            device_info = self.audio.get_device_info_by_index(self.selected_mp3_output_device)
            device_name = device_info.get('name', '未知设备')

            # 加载MP3文件
            audio_segment = AudioSegment.from_mp3(self.current_mp3)

            # 转换为PyAudio兼容的格式
            self.mp3_audio_data = audio_segment.raw_data
            self.mp3_sample_rate = audio_segment.frame_rate
            self.mp3_channels = audio_segment.channels
            self.mp3_sample_width = audio_segment.sample_width

            # 计算PyAudio格式
            if self.mp3_sample_width == 1:
                audio_format = pyaudio.paUInt8
            elif self.mp3_sample_width == 2:
                audio_format = pyaudio.paInt16
            elif self.mp3_sample_width == 4:
                audio_format = pyaudio.paInt32
            else:
                audio_format = pyaudio.paInt16

            # 打开音频流
            self.mp3_stream = self.audio.open(
                format=audio_format,
                channels=self.mp3_channels,
                rate=self.mp3_sample_rate,
                output=True,
                output_device_index=self.selected_mp3_output_device,
                frames_per_buffer=1024
            )

            # 开始播放线程
            self.mp3_playing = True
            self.mp3_paused = False
            self.mp3_thread = threading.Thread(target=self.mp3_playback_worker)
            self.mp3_thread.daemon = True
            self.mp3_thread.start()

            self.print_success(f"MP3开始播放到: {device_name}")

        except Exception as e:
            self.print_error(f"启动MP3播放失败: {str(e)}")

    def mp3_playback_worker(self):
        """MP3播放工作线程"""
        try:
            chunk_size = 1024 * self.mp3_channels * self.mp3_sample_width
            data_len = len(self.mp3_audio_data)
            position = 0

            while self.mp3_playing and position < data_len:
                if not self.mp3_paused:
                    end_pos = min(position + chunk_size, data_len)
                    chunk = self.mp3_audio_data[position:end_pos]

                    if len(chunk) > 0:
                        # 应用音量调节
                        if self.mp3_volume != 1.0:
                            if self.mp3_sample_width == 2:
                                audio_array = np.frombuffer(chunk, dtype=np.int16)
                                audio_array = (audio_array * self.mp3_volume).astype(np.int16)
                                chunk = audio_array.tobytes()

                        self.mp3_stream.write(chunk)
                        position = end_pos
                    else:
                        break
                else:
                    time.sleep(0.1)

            # 播放完成
            self.mp3_playing = False
            print(f"\n{Colors.OKGREEN}MP3播放完成{Colors.ENDC}")

        except Exception as e:
            self.mp3_playing = False
            print(f"\n{Colors.FAIL}MP3播放错误: {str(e)}{Colors.ENDC}")

    def stop_mp3(self):
        """停止MP3播放"""
        try:
            self.mp3_playing = False
            self.mp3_paused = False

            if self.mp3_thread:
                self.mp3_thread.join(timeout=1.0)

            if self.mp3_stream:
                self.mp3_stream.stop_stream()
                self.mp3_stream.close()
                self.mp3_stream = None

            self.print_success("MP3播放已停止")

        except Exception as e:
            self.print_error(f"停止MP3失败: {str(e)}")

    def adjust_mp3_volume(self):
        """调整MP3音量"""
        current_volume = int(self.mp3_volume * 100)
        print(f"\n当前MP3音量: {Colors.OKCYAN}{current_volume}%{Colors.ENDC}")

        try:
            new_volume = input("请输入新音量 (0-100) [回车保持当前]: ").strip()
            if new_volume == "":
                return

            volume = int(new_volume)
            if 0 <= volume <= 100:
                self.mp3_volume = volume / 100.0
                self.print_success(f"MP3音量已设置为 {volume}%")
            else:
                self.print_error("音量必须在0-100之间")
        except ValueError:
            self.print_error("请输入有效的数字")

    def toggle_mp3_mute(self):
        """切换MP3静音状态"""
        if self.mp3_volume > 0:
            self.mp3_prev_volume = self.mp3_volume
            self.mp3_volume = 0.0
            self.print_success("MP3已静音")
        else:
            prev_volume = getattr(self, 'mp3_prev_volume', 0.7)
            self.mp3_volume = prev_volume
            volume_percent = int(prev_volume * 100)
            self.print_success(f"MP3已取消静音，音量: {volume_percent}%")

    def system_audio_menu(self):
        """系统音频捕获设置菜单"""
        while True:
            self.print_header("系统音频捕获设置")

            if not SOUNDCARD_AVAILABLE:
                self.print_error("系统音频捕获功能不可用")
                print("请安装soundcard库: pip install soundcard")
                input(f"\n{Colors.OKCYAN}按回车键返回...{Colors.ENDC}")
                break

            status = "启用" if self.system_audio_mode else "禁用"
            status_color = Colors.OKGREEN if self.system_audio_mode else Colors.FAIL
            print(f"系统音频捕获模式: {status_color}{status}{Colors.ENDC}")

            print(f"\n{Colors.BOLD}选项:{Colors.ENDC}")
            toggle_text = "禁用" if self.system_audio_mode else "启用"
            print(f"  1. {toggle_text}系统音频捕获模式")
            print("  2. 查看说明")
            print("  0. 返回主菜单")

            choice = input(f"\n{Colors.BOLD}请选择操作: {Colors.ENDC}").strip()

            if choice == "0":
                break
            elif choice == "1":
                self.toggle_system_audio_mode()
            elif choice == "2":
                self.show_system_audio_help()
            else:
                self.print_error("无效的选择")

            input(f"\n{Colors.OKCYAN}按回车键继续...{Colors.ENDC}")

    def toggle_system_audio_mode(self):
        """切换系统音频捕获模式"""
        if not SOUNDCARD_AVAILABLE:
            self.print_error("系统音频捕获需要soundcard库")
            return

        self.system_audio_mode = not self.system_audio_mode

        if self.system_audio_mode:
            self.print_success("系统音频捕获模式已启用")
            print("现在可以直接捕获系统播放的音频")
        else:
            if self.is_routing:
                self.stop_routing()
            self.print_success("已切换到普通音频路由模式")

    def show_system_audio_help(self):
        """显示系统音频捕获说明"""
        self.print_header("系统音频捕获说明")

        print(f"{Colors.BOLD}功能说明:{Colors.ENDC}")
        print("• 直接捕获系统播放的所有音频")
        print("• 无需物理麦克风输入")
        print("• 可以录制音乐、视频、游戏等系统音频")

        print(f"\n{Colors.BOLD}使用方法:{Colors.ENDC}")
        print("1. 启用系统音频捕获模式")
        print("2. 选择输出设备（通常是VB-Cable Input）")
        print("3. 开始音频路由")
        print("4. 系统播放的音频将被路由到选定设备")

        print(f"\n{Colors.BOLD}注意事项:{Colors.ENDC}")
        print("• 需要安装soundcard库: pip install soundcard")
        print("• 在此模式下输入设备选择被忽略")
        print("• 可能会捕获到所有系统音频，包括通知声音")

    def isolation_mode_menu(self):
        """音频隔离模式设置菜单"""
        while True:
            self.print_header("音频隔离模式设置")

            status = "启用" if self.isolation_mode else "禁用"
            status_color = Colors.OKGREEN if self.isolation_mode else Colors.FAIL
            print(f"音频隔离模式: {status_color}{status}{Colors.ENDC}")

            print(f"\n{Colors.BOLD}选项:{Colors.ENDC}")
            toggle_text = "禁用" if self.isolation_mode else "启用"
            print(f"  1. {toggle_text}音频隔离模式")
            print("  2. 查看说明")
            print("  0. 返回主菜单")

            choice = input(f"\n{Colors.BOLD}请选择操作: {Colors.ENDC}").strip()

            if choice == "0":
                break
            elif choice == "1":
                self.toggle_isolation_mode()
            elif choice == "2":
                self.show_isolation_help()
            else:
                self.print_error("无效的选择")

            input(f"\n{Colors.OKCYAN}按回车键继续...{Colors.ENDC}")

    def toggle_isolation_mode(self):
        """切换音频隔离模式"""
        self.isolation_mode = not self.isolation_mode

        if self.isolation_mode:
            self.print_success("音频隔离模式已启用")
            print("现在麦克风音频和MP3音频将分别处理，避免混音")

            if self.system_audio_mode:
                self.print_warning("建议关闭系统音频捕获模式以获得更好的隔离效果")
        else:
            self.print_success("音频隔离模式已禁用")

    def show_isolation_help(self):
        """显示音频隔离模式说明"""
        self.print_header("音频隔离模式说明")

        print(f"{Colors.BOLD}功能说明:{Colors.ENDC}")
        print("• 防止系统音频混入录音中")
        print("• 麦克风音频 → 路由到选定输出设备")
        print("• MP3音频 → 播放到指定设备")
        print("• 后端应用音频 → 不会混入录音")

        print(f"\n{Colors.BOLD}使用场景:{Colors.ENDC}")
        print("• 直播时需要分离麦克风和背景音乐")
        print("• 录制时避免系统通知音混入")
        print("• 需要精确控制音频源的场合")

        print(f"\n{Colors.BOLD}配置建议:{Colors.ENDC}")
        print("• 麦克风 → VB-Cable Input (录音软件可以捕获)")
        print("• MP3 → 物理音频设备 (只有你能听到)")
        print("• 或者临时设置系统默认设备为VB-Cable")

    def start_system_audio_capture(self):
        """启动系统音频捕获"""
        if not SOUNDCARD_AVAILABLE:
            self.print_error("系统音频捕获需要soundcard库。请运行: pip install soundcard")
            return

        try:
            if self.selected_output_device is None:
                self.print_error("请先选择输出设备")
                return

            # 获取输出设备信息
            output_info = self.audio.get_device_info_by_index(self.selected_output_device)

            # 确定音频参数
            output_channels = int(output_info['maxOutputChannels'])
            output_rate = int(output_info['defaultSampleRate'])

            channels = min(2, output_channels) if output_channels > 0 else 2

            if output_rate < 8000:
                sample_rate = 44100
            elif output_rate > 48000:
                sample_rate = 48000
            else:
                sample_rate = output_rate

            self.print_status(f"使用音频参数: 采样率={sample_rate}Hz, 通道={channels}")

            # 获取系统默认音频设备
            default_speaker = sc.default_speaker()
            self.system_mic = sc.get_microphone(id=str(default_speaker.id), include_loopback=True)

            # 保存参数
            self.capture_sample_rate = sample_rate
            self.capture_channels = channels
            self.capture_output_device = self.selected_output_device

            # 开始捕获线程
            self.is_routing = True
            self.routing_thread = threading.Thread(target=self.system_audio_worker)
            self.routing_thread.daemon = True
            self.routing_thread.start()

            self.print_success(f"系统音频捕获已启动 ({sample_rate}Hz, {channels}ch)")

        except Exception as e:
            self.print_error(f"启动系统音频捕获失败: {str(e)}")
            self.system_audio_mode = False

    def stop_system_audio_capture(self):
        """停止系统音频捕获"""
        self.is_routing = False

        if self.routing_thread:
            self.routing_thread.join(timeout=1.0)

        if self.system_mic:
            self.system_mic = None

        self.print_success("系统音频捕获已停止")

    def system_audio_worker(self):
        """系统音频捕获工作线程"""
        if not SOUNDCARD_AVAILABLE:
            return

        try:
            sample_rate = getattr(self, 'capture_sample_rate', 44100)
            channels = getattr(self, 'capture_channels', 2)
            output_device = getattr(self, 'capture_output_device', None)

            if output_device is None:
                return

            # 打开输出流
            output_stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=channels,
                rate=sample_rate,
                output=True,
                output_device_index=output_device,
                frames_per_buffer=1024
            )

            # 开始录制系统音频
            with self.system_mic.recorder(samplerate=sample_rate, channels=channels) as recorder:
                while self.is_routing:
                    try:
                        data = recorder.record(numframes=1024)

                        if not self.routing_muted and len(data) > 0:
                            # 应用音量调节
                            if self.routing_volume != 1.0:
                                data = data * self.routing_volume

                            # 处理通道数
                            if len(data.shape) == 1:
                                if channels == 2:
                                    data = np.column_stack((data, data))
                            elif len(data.shape) == 2 and data.shape[1] > channels:
                                data = np.mean(data[:, :channels], axis=1, keepdims=True)
                                if channels == 2:
                                    data = np.column_stack((data.flatten(), data.flatten()))

                            # 转换格式并输出
                            audio_data = np.clip(data * 32767, -32768, 32767).astype(np.int16)
                            audio_bytes = audio_data.tobytes()
                            output_stream.write(audio_bytes)

                    except Exception as e:
                        print(f"系统音频捕获错误: {str(e)}")
                        continue

            output_stream.stop_stream()
            output_stream.close()

        except Exception as e:
            print(f"\n{Colors.FAIL}系统音频捕获工作线程错误: {str(e)}{Colors.ENDC}")

    def start_isolated_audio_routing(self):
        """启动隔离的音频路由"""
        try:
            if self.selected_input_device is None or self.selected_output_device is None:
                self.print_error("请选择输入和输出设备")
                return

            if self.selected_input_device == self.selected_output_device:
                self.print_error("输入和输出设备不能相同")
                return

            # 标准音频参数
            FORMAT = pyaudio.paInt16
            CHANNELS = 2
            RATE = 44100
            CHUNK = 1024

            # 打开音频流
            self.routing_stream_in = self.audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                input_device_index=self.selected_input_device,
                frames_per_buffer=CHUNK
            )

            self.routing_stream_out = self.audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                output=True,
                output_device_index=self.selected_output_device,
                frames_per_buffer=CHUNK
            )

            # 开始隔离路由线程
            self.is_routing = True
            self.routing_thread = threading.Thread(target=self.isolated_routing_worker)
            self.routing_thread.daemon = True
            self.routing_thread.start()

            self.print_success("音频隔离路由已启动")

        except Exception as e:
            self.print_error(f"启动隔离音频路由失败: {str(e)}")
            self.stop_routing()

    def isolated_routing_worker(self):
        """隔离音频路由工作线程"""
        try:
            while self.is_routing:
                mic_data = self.routing_stream_in.read(1024, exception_on_overflow=False)

                if not self.routing_muted and len(mic_data) > 0:
                    try:
                        if self.routing_volume != 1.0:
                            mic_audio = np.frombuffer(mic_data, dtype=np.int16)
                            if len(mic_audio) > 0:
                                scaled_mic = mic_audio.astype(np.float64) * self.routing_volume
                                scaled_mic = np.clip(scaled_mic, -32768, 32767)
                                mic_data = scaled_mic.astype(np.int16).tobytes()
                    except (ValueError, MemoryError):
                        pass

                    self.routing_stream_out.write(mic_data)

        except Exception as e:
            print(f"\n{Colors.FAIL}隔离音频路由错误: {str(e)}{Colors.ENDC}")
            self.is_routing = False

    def cleanup(self):
        """清理资源"""
        self.stop_routing()
        self.stop_mp3()
        if hasattr(self, 'audio'):
            self.audio.terminate()

    def run(self):
        """运行主程序"""
        try:
            self.print_header("音频路由工具 - 命令行版")
            self.print_colored("VB-Cable伴侣工具 (增强版)", Colors.OKCYAN)

            # 显示初始状态
            vb_cable_found = any("CABLE" in name.upper() for _, name in self.input_devices + self.output_devices)
            if vb_cable_found:
                self.print_success("已自动检测并选择VB-Cable设备")
            else:
                self.print_warning("未检测到VB-Cable设备，已选择默认设备")

            # 主循环
            while True:
                try:
                    choice = self.show_main_menu()

                    if choice == "0":
                        self.print_colored("感谢使用！", Colors.OKGREEN)
                        break
                    elif choice == "1":
                        self.show_devices()
                    elif choice == "2":
                        self.select_input_device()
                    elif choice == "3":
                        self.select_output_device()
                    elif choice == "4":
                        self.select_mp3_output_device()
                    elif choice == "5":
                        self.show_current_status()
                    elif choice == "6":
                        self.audio_routing_menu()
                    elif choice == "7":
                        self.mp3_control_menu()
                    elif choice == "8":
                        self.system_audio_menu()
                    elif choice == "9":
                        self.isolation_mode_menu()
                    else:
                        self.print_error("无效的选择，请输入0-9")

                    if choice != "6" and choice != "7" and choice != "8" and choice != "9":
                        input(f"\n{Colors.OKCYAN}按回车键继续...{Colors.ENDC}")

                except KeyboardInterrupt:
                    print(f"\n{Colors.WARNING}收到中断信号{Colors.ENDC}")
                    break
                except Exception as e:
                    self.print_error(f"程序错误: {str(e)}")
                    input(f"\n{Colors.OKCYAN}按回车键继续...{Colors.ENDC}")

        finally:
            self.cleanup()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="音频路由工具 - 命令行版")
    parser.add_argument("--version", action="version", version="AudioRouter CLI v1.0")
    parser.add_argument("--list-devices", action="store_true", help="列出所有音频设备并退出")
    parser.add_argument("--auto-start", action="store_true", help="自动开始路由（使用默认设备）")

    args = parser.parse_args()

    try:
        router = AudioRouterCLI()

        if args.list_devices:
            router.show_devices()
            return

        if args.auto_start:
            router.print_status("自动启动模式")
            router.start_routing()

            try:
                while router.is_routing:
                    time.sleep(1)
            except KeyboardInterrupt:
                router.print_colored("\n收到中断信号，停止路由", Colors.WARNING)
                router.stop_routing()
            return

        # 正常交互模式
        router.run()

    except Exception as e:
        print(f"{Colors.FAIL}程序启动失败: {str(e)}{Colors.ENDC}")
        sys.exit(1)

if __name__ == "__main__":
    main()
